import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import { sendEmail } from '@/lib/email';

export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication and admin role
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || (decoded.role !== 'admin' && decoded.role !== 'super_admin')) {
      return apiResponse.error('Admin access required', 403);
    }
    
    const serviceId = params.id;
    
    // Find the service
    const service = await Service.findById(serviceId)
      .populate('businessOwner', 'businessName ownerFirstName ownerLastName email');
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    // Check for active bookings
    const activeBookings = await Booking.find({
      service: serviceId,
      status: { $in: ['pending', 'confirmed', 'in_progress'] }
    }).populate('user', 'firstName lastName email');
    
    if (activeBookings.length > 0) {
      return apiResponse.error(
        `Cannot delete service with ${activeBookings.length} active booking(s). Please handle these bookings first.`,
        400
      );
    }
    
    // Store service details for email notification
    const serviceDetails = {
      title: service.title,
      businessOwnerEmail: service.businessOwner.email,
      businessOwnerName: service.businessOwner.ownerFirstName
    };
    
    // Delete the service
    await Service.findByIdAndDelete(serviceId);
    
    // Update any completed bookings to mark service as deleted
    await Booking.updateMany(
      { service: serviceId },
      { $set: { serviceDeleted: true } }
    );
    
    // Send email notification to business owner
    try {
      await sendEmail({
        to: serviceDetails.businessOwnerEmail,
        subject: `Service Removed - ${serviceDetails.title}`,
        html: `
          <h2>Service Removal Notification</h2>
          <p>Dear ${serviceDetails.businessOwnerName},</p>
          <p>Your service "<strong>${serviceDetails.title}</strong>" has been removed from the platform by our admin team.</p>
          
          <p>This action may have been taken due to:</p>
          <ul>
            <li>Policy violations</li>
            <li>Quality concerns</li>
            <li>Customer complaints</li>
            <li>Administrative reasons</li>
          </ul>
          
          <p>If you believe this was done in error or have questions about this decision, please contact our support team immediately.</p>
          
          <p>You can create new services through your business dashboard, ensuring they comply with our platform guidelines.</p>
          
          <p>Best regards,<br>The BookMyService Team</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send service deletion email:', emailError);
      // Don't fail the request if email fails
    }
    
    return apiResponse.success(
      { deletedServiceId: serviceId }, 
      'Service deleted successfully'
    );
    
  } catch (error) {
    console.error('Error deleting service:', error);
    return apiResponse.error('Failed to delete service', 500);
  }
}

export async function GET(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication and admin role
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || (decoded.role !== 'admin' && decoded.role !== 'super_admin')) {
      return apiResponse.error('Admin access required', 403);
    }
    
    const serviceId = params.id;
    
    // Find the service with detailed information
    const service = await Service.findById(serviceId)
      .populate('businessOwner', 'businessName ownerFirstName ownerLastName email phone');
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    // Get booking statistics for this service
    const bookingStats = await Booking.aggregate([
      { $match: { service: service._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$totalAmount' }
        }
      }
    ]);
    
    const totalBookings = await Booking.countDocuments({ service: serviceId });
    const totalRevenue = await Booking.aggregate([
      { $match: { service: service._id, status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } }
    ]);
    
    return apiResponse.success({
      service,
      stats: {
        totalBookings,
        totalRevenue: totalRevenue[0]?.total || 0,
        bookingsByStatus: bookingStats
      }
    }, 'Service details fetched successfully');
    
  } catch (error) {
    console.error('Error fetching service details:', error);
    return apiResponse.error('Failed to fetch service details', 500);
  }
}
