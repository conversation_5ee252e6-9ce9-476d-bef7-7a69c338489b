import mongoose from 'mongoose';

const ServiceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Service title is required'],
    trim: true,
    maxlength: [100, 'Service title cannot be more than 100 characters']
  },
  name: {
    type: String,
    required: [true, 'Service name is required'],
    trim: true,
    maxlength: [100, 'Service name cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Service description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  category: {
    type: String,
    required: [true, 'Service category is required'],
    enum: [
      'Home & Garden',
      'Health & Wellness',
      'Automotive',
      'Technology',
      'Education',
      'Events & Entertainment',
      'Business Services',
      'Personal Care',
      'Cleaning',
      'Repair & Maintenance',
      'Other'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  price: {
    type: Number,
    required: [true, 'Service price is required'],
    min: [0, 'Price cannot be negative']
  },
  priceType: {
    type: String,
    enum: ['fixed', 'hourly', 'per_project'],
    default: 'fixed'
  },
  duration: {
    type: Number, // in minutes
    required: [true, 'Service duration is required'],
    min: [15, 'Duration must be at least 15 minutes']
  },
  businessOwner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Business owner is required']
  },
  images: [{
    url: String,
    publicId: String, // for Cloudinary
    alt: String
  }],
  availability: {
    type: String,
    enum: ['available', 'busy', 'unavailable'],
    default: 'available'
  },
  serviceAreas: [{
    city: String,
    state: String,
    zipCodes: [String],
    radius: Number // in miles
  }],
  bookingType: {
    type: String,
    enum: ['online', 'in_person', 'both'],
    default: 'in_person'
  },
  requirements: [String], // What customer needs to provide
  includes: [String], // What's included in the service
  excludes: [String], // What's not included
  tags: [String],
  rating: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 }
  },
  reviews: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    rating: { type: Number, min: 1, max: 5 },
    comment: String,
    createdAt: { type: Date, default: Date.now }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  totalBookings: {
    type: Number,
    default: 0
  },
  completedBookings: {
    type: Number,
    default: 0
  },
  // SEO fields
  slug: {
    type: String,
    unique: true,
    sparse: true
  },
  metaTitle: String,
  metaDescription: String,
  // Scheduling
  advanceBookingDays: {
    type: Number,
    default: 30,
    min: 1
  },
  cancellationPolicy: {
    type: String,
    enum: ['flexible', 'moderate', 'strict'],
    default: 'moderate'
  },
  // Pricing details
  additionalCharges: [{
    name: String,
    amount: Number,
    type: { type: String, enum: ['fixed', 'percentage'] }
  }],
  discounts: [{
    name: String,
    amount: Number,
    type: { type: String, enum: ['fixed', 'percentage'] },
    validFrom: Date,
    validTo: Date,
    isActive: { type: Boolean, default: true }
  }],

  // Service status
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'suspended'],
    default: 'active'
  }
}, {
  timestamps: true
});

// Indexes
ServiceSchema.index({ businessOwner: 1 });
ServiceSchema.index({ category: 1 });
ServiceSchema.index({ 'serviceAreas.city': 1 });
ServiceSchema.index({ 'serviceAreas.state': 1 });
ServiceSchema.index({ isActive: 1 });
ServiceSchema.index({ isFeatured: 1 });
ServiceSchema.index({ 'rating.average': -1 });
ServiceSchema.index({ totalBookings: -1 });
ServiceSchema.index({ createdAt: -1 });
ServiceSchema.index({ name: 'text', description: 'text', tags: 'text' });

// Virtual for formatted price
ServiceSchema.virtual('formattedPrice').get(function() {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  });
  return formatter.format(this.price);
});

// Virtual for duration in hours
ServiceSchema.virtual('durationHours').get(function() {
  return Math.round((this.duration / 60) * 100) / 100;
});

// Virtual for success rate
ServiceSchema.virtual('successRate').get(function() {
  if (this.totalBookings === 0) return 0;
  return Math.round((this.completedBookings / this.totalBookings) * 100);
});

// Ensure virtual fields are serialized
ServiceSchema.set('toJSON', { virtuals: true });
ServiceSchema.set('toObject', { virtuals: true });

// Pre-save middleware to generate slug
ServiceSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Instance methods
ServiceSchema.methods.addReview = function(userId, rating, comment) {
  this.reviews.push({
    user: userId,
    rating,
    comment
  });
  
  // Recalculate average rating
  const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0);
  this.rating.average = totalRating / this.reviews.length;
  this.rating.count = this.reviews.length;
  
  return this.save();
};

ServiceSchema.methods.incrementBookings = function() {
  this.totalBookings += 1;
  return this.save();
};

ServiceSchema.methods.incrementCompletedBookings = function() {
  this.completedBookings += 1;
  return this.save();
};

ServiceSchema.methods.isAvailableInArea = function(city, state) {
  return this.serviceAreas.some(area => 
    area.city.toLowerCase() === city.toLowerCase() && 
    area.state.toLowerCase() === state.toLowerCase()
  );
};

export default mongoose.models.Service || mongoose.model('Service', ServiceSchema);
