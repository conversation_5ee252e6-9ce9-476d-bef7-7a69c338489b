import mongoose from 'mongoose';

const employeeSchema = new mongoose.Schema({
  // Basic Information
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone number is required'],
    match: [/^\+?[\d\s\-\(\)]+$/, 'Please enter a valid phone number']
  },
  
  // Authentication
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  role: {
    type: String,
    default: 'employee',
    enum: ['employee']
  },
  
  // Business Association
  businessOwner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Business owner is required']
  },
  
  // Employee Details
  employeeId: {
    type: String,
    unique: true,
    sparse: true // Allows multiple null values
  },
  position: {
    type: String,
    required: [true, 'Position/Job title is required'],
    trim: true
  },
  department: {
    type: String,
    trim: true,
    default: 'General'
  },
  
  // Contact & Personal Info
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: { type: String, default: 'USA' }
  },
  dateOfBirth: Date,
  emergencyContact: {
    name: String,
    relationship: String,
    phoneNumber: String
  },
  
  // Employment Details
  hireDate: {
    type: Date,
    default: Date.now
  },
  employmentType: {
    type: String,
    enum: ['full-time', 'part-time', 'contract', 'freelance'],
    default: 'full-time'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'terminated'],
    default: 'active'
  },
  
  // Skills & Qualifications
  skills: [{
    name: String,
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'expert'],
      default: 'intermediate'
    },
    certified: { type: Boolean, default: false }
  }],
  certifications: [{
    name: String,
    issuedBy: String,
    issuedDate: Date,
    expiryDate: Date,
    certificateNumber: String
  }],
  
  // Service Assignments
  assignedServices: [{
    service: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Service'
    },
    assignedDate: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    },
    specialization: String // e.g., "Lead technician", "Assistant", etc.
  }],
  
  // Availability & Schedule
  availability: {
    monday: { available: Boolean, startTime: String, endTime: String },
    tuesday: { available: Boolean, startTime: String, endTime: String },
    wednesday: { available: Boolean, startTime: String, endTime: String },
    thursday: { available: Boolean, startTime: String, endTime: String },
    friday: { available: Boolean, startTime: String, endTime: String },
    saturday: { available: Boolean, startTime: String, endTime: String },
    sunday: { available: Boolean, startTime: String, endTime: String }
  },
  timeOff: [{
    startDate: Date,
    endDate: Date,
    reason: String,
    approved: { type: Boolean, default: false },
    requestedDate: { type: Date, default: Date.now }
  }],
  
  // Performance & Ratings
  performanceMetrics: {
    totalBookings: { type: Number, default: 0 },
    completedBookings: { type: Number, default: 0 },
    cancelledBookings: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0, min: 0, max: 5 },
    totalRatings: { type: Number, default: 0 },
    totalEarnings: { type: Number, default: 0 }
  },
  
  // Compensation
  compensation: {
    type: {
      type: String,
      enum: ['hourly', 'salary', 'commission', 'per-service'],
      default: 'hourly'
    },
    amount: Number,
    currency: { type: String, default: 'USD' },
    commissionRate: Number // For commission-based employees
  },
  
  // Profile & Preferences
  profileImage: String,
  bio: String,
  languages: [String],
  preferences: {
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: true },
      push: { type: Boolean, default: true }
    },
    workRadius: Number, // Maximum distance willing to travel
    preferredServiceTypes: [String]
  },
  
  // System Fields
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  isPhoneVerified: {
    type: Boolean,
    default: false
  },
  lastLogin: Date,
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: Date,
  
  // Audit Fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
employeeSchema.index({ businessOwner: 1, status: 1 });
employeeSchema.index({ email: 1 });
employeeSchema.index({ employeeId: 1 });
employeeSchema.index({ 'assignedServices.service': 1 });

// Virtual for full name
employeeSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for completion rate
employeeSchema.virtual('completionRate').get(function() {
  if (this.performanceMetrics.totalBookings === 0) return 0;
  return (this.performanceMetrics.completedBookings / this.performanceMetrics.totalBookings * 100).toFixed(2);
});

// Virtual for account locked status
employeeSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to generate employee ID
employeeSchema.pre('save', async function(next) {
  if (this.isNew && !this.employeeId) {
    const count = await this.constructor.countDocuments({ businessOwner: this.businessOwner });
    this.employeeId = `EMP${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Methods
employeeSchema.methods.addServiceAssignment = function(serviceId, specialization = '') {
  const existingAssignment = this.assignedServices.find(
    assignment => assignment.service.toString() === serviceId.toString()
  );
  
  if (!existingAssignment) {
    this.assignedServices.push({
      service: serviceId,
      specialization,
      isActive: true
    });
  } else {
    existingAssignment.isActive = true;
    existingAssignment.specialization = specialization;
  }
  
  return this.save();
};

employeeSchema.methods.removeServiceAssignment = function(serviceId) {
  this.assignedServices = this.assignedServices.filter(
    assignment => assignment.service.toString() !== serviceId.toString()
  );
  return this.save();
};

employeeSchema.methods.updatePerformanceMetrics = function(bookingData) {
  const metrics = this.performanceMetrics;
  
  if (bookingData.status === 'completed') {
    metrics.completedBookings += 1;
    if (bookingData.rating) {
      const totalRatingPoints = metrics.averageRating * metrics.totalRatings;
      metrics.totalRatings += 1;
      metrics.averageRating = (totalRatingPoints + bookingData.rating) / metrics.totalRatings;
    }
    if (bookingData.earnings) {
      metrics.totalEarnings += bookingData.earnings;
    }
  } else if (bookingData.status === 'cancelled') {
    metrics.cancelledBookings += 1;
  }
  
  metrics.totalBookings += 1;
  return this.save();
};

const Employee = mongoose.models.Employee || mongoose.model('Employee', employeeSchema);

export default Employee;
