// "use client";

// import React, { useState, useEffect } from 'react';
// import {
//   Dialog,
//   DialogTitle,
//   DialogContent,
//   DialogActions,
//   TextField,
//   Button,
//   FormControl,
//   InputLabel,
//   Select,
//   MenuItem,
//   Grid,
//   Box,
//   Typography,
//   Chip,
//   IconButton,
//   InputAdornment
// } from '@mui/material';
// import { Close as CloseIcon, Add as AddIcon } from '@mui/icons-material';
// import Swal from 'sweetalert2';

// const SERVICE_CATEGORIES = [
//   'Home & Garden',
//   'Health & Wellness',
//   'Automotive',
//   'Technology',
//   'Education',
//   'Events & Entertainment',
//   'Business Services',
//   'Personal Care',
//   'Cleaning',
//   'Repair & Maintenance',
//   'Other'
// ];

// const ServiceModal = ({ open, onClose, onSave, service = null, loading = false }) => {
//   const [formData, setFormData] = useState({
//     title: '',
//     name: '',
//     description: '',
//     category: '',
//     subcategory: '',
//     price: '',
//     duration: '',
//     priceType: 'fixed',
//     bookingType: 'in_person',
//     availability: 'available',
//     serviceAreas: [{
//       city: '',
//       state: '',
//       zipCodes: '',
//       radius: ''
//     }],
//     requirements: [],
//     includes: [],
//     excludes: [],
//     tags: [],
//     newTag: '',
//     newRequirement: '',
//     newInclude: '',
//     newExclude: ''
//   });

//   const [errors, setErrors] = useState({});

//   useEffect(() => {
//     if (service) {
//       // Edit mode - populate form with service data
//       setFormData({
//         title: service.title || '',
//         name: service.name || '',
//         description: service.description || '',
//         category: service.category || '',
//         subcategory: service.subcategory || '',
//         price: service.price?.toString() || '',
//         duration: service.duration?.toString() || '',
//         priceType: service.priceType || 'fixed',
//         bookingType: service.bookingType || 'in_person',
//         availability: service.availability || 'available',
//         serviceAreas: service.serviceAreas?.length > 0 ? service.serviceAreas.map(area => ({
//           city: area.city || '',
//           state: area.state || '',
//           zipCodes: Array.isArray(area.zipCodes) ? area.zipCodes.join(', ') : '',
//           radius: area.radius?.toString() || ''
//         })) : [{
//           city: '',
//           state: '',
//           zipCodes: '',
//           radius: ''
//         }],
//         requirements: service.requirements || [],
//         includes: service.includes || [],
//         excludes: service.excludes || [],
//         tags: service.tags || [],
//         newTag: '',
//         newRequirement: '',
//         newInclude: '',
//         newExclude: ''
//       });
//     } else {
//       // Add mode - reset form
//       setFormData({
//         title: '',
//         name: '',
//         description: '',
//         category: '',
//         subcategory: '',
//         price: '',
//         duration: '',
//         priceType: 'fixed',
//         bookingType: 'in_person',
//         availability: 'available',
//         serviceAreas: [{
//           city: '',
//           state: '',
//           zipCodes: '',
//           radius: ''
//         }],
//         requirements: [],
//         includes: [],
//         excludes: [],
//         tags: [],
//         newTag: '',
//         newRequirement: '',
//         newInclude: '',
//         newExclude: ''
//       });
//     }
//     setErrors({});
//   }, [service, open]);

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setFormData(prev => ({
//       ...prev,
//       [name]: value
//     }));
    
//     // Clear error when user starts typing
//     if (errors[name]) {
//       setErrors(prev => ({
//         ...prev,
//         [name]: ''
//       }));
//     }
//   };

//   const handleAddTag = () => {
//     if (formData.newTag.trim() && !formData.tags.includes(formData.newTag.trim())) {
//       setFormData(prev => ({
//         ...prev,
//         tags: [...prev.tags, prev.newTag.trim()],
//         newTag: ''
//       }));
//     }
//   };

//   const handleRemoveTag = (tagToRemove) => {
//     setFormData(prev => ({
//       ...prev,
//       tags: prev.tags.filter(tag => tag !== tagToRemove)
//     }));
//   };

//   const handleAddRequirement = () => {
//     if (formData.newRequirement.trim() && !formData.requirements.includes(formData.newRequirement.trim())) {
//       setFormData(prev => ({
//         ...prev,
//         requirements: [...prev.requirements, prev.newRequirement.trim()],
//         newRequirement: ''
//       }));
//     }
//   };

//   const handleRemoveRequirement = (itemToRemove) => {
//     setFormData(prev => ({
//       ...prev,
//       requirements: prev.requirements.filter(item => item !== itemToRemove)
//     }));
//   };

//   const handleAddInclude = () => {
//     if (formData.newInclude.trim() && !formData.includes.includes(formData.newInclude.trim())) {
//       setFormData(prev => ({
//         ...prev,
//         includes: [...prev.includes, prev.newInclude.trim()],
//         newInclude: ''
//       }));
//     }
//   };

//   const handleRemoveInclude = (itemToRemove) => {
//     setFormData(prev => ({
//       ...prev,
//       includes: prev.includes.filter(item => item !== itemToRemove)
//     }));
//   };

//   const handleAddExclude = () => {
//     if (formData.newExclude.trim() && !formData.excludes.includes(formData.newExclude.trim())) {
//       setFormData(prev => ({
//         ...prev,
//         excludes: [...prev.excludes, prev.newExclude.trim()],
//         newExclude: ''
//       }));
//     }
//   };

//   const handleRemoveExclude = (itemToRemove) => {
//     setFormData(prev => ({
//       ...prev,
//       excludes: prev.excludes.filter(item => item !== itemToRemove)
//     }));
//   };

//   const handleServiceAreaChange = (index, field, value) => {
//     setFormData(prev => ({
//       ...prev,
//       serviceAreas: prev.serviceAreas.map((area, i) =>
//         i === index ? { ...area, [field]: value } : area
//       )
//     }));
//   };

//   const validateForm = () => {
//     const newErrors = {};

//     if (!formData.title.trim()) newErrors.title = 'Title is required';
//     if (!formData.name.trim()) newErrors.name = 'Name is required';
//     if (!formData.description.trim()) newErrors.description = 'Description is required';
//     if (!formData.category) newErrors.category = 'Category is required';
//     if (!formData.price || parseFloat(formData.price) <= 0) newErrors.price = 'Valid price is required';
//     if (!formData.duration || parseInt(formData.duration) <= 0) newErrors.duration = 'Valid duration is required';

//     // Validate service areas
//     const hasValidServiceArea = formData.serviceAreas.some(area =>
//       area.city.trim() || area.state.trim()
//     );
//     if (!hasValidServiceArea) {
//       newErrors.serviceAreas = 'At least one service area with city or state is required';
//     }

//     setErrors(newErrors);
//     return Object.keys(newErrors).length === 0;
//   };

//   const handleSubmit = async () => {
//     if (!validateForm()) {
//       Swal.fire({
//         icon: 'error',
//         title: 'Validation Error',
//         text: 'Please fill in all required fields correctly.',
//         confirmButtonColor: '#3085d6'
//       });
//       return;
//     }

//     const serviceData = {
//       ...formData,
//       price: parseFloat(formData.price),
//       duration: parseInt(formData.duration),
//       tags: formData.tags,
//       requirements: formData.requirements,
//       includes: formData.includes,
//       excludes: formData.excludes,
//       serviceAreas: formData.serviceAreas.map(area => ({
//         city: area.city.trim(),
//         state: area.state.trim(),
//         zipCodes: area.zipCodes ? area.zipCodes.split(',').map(zip => zip.trim()).filter(zip => zip) : [],
//         radius: area.radius ? parseInt(area.radius) : undefined
//       })).filter(area => area.city || area.state) // Only include areas with city or state
//     };

//     // Remove temporary fields from the data being sent
//     delete serviceData.newTag;
//     delete serviceData.newRequirement;
//     delete serviceData.newInclude;
//     delete serviceData.newExclude;

//     try {
//       await onSave(serviceData);
//       onClose();
//     } catch (error) {
//       console.error('Error saving service:', error);
//     }
//   };

//   const handleClose = () => {
//     // Create comparison object with all fields
//     const originalData = {
//       title: service?.title || '',
//       name: service?.name || '',
//       description: service?.description || '',
//       category: service?.category || '',
//       subcategory: service?.subcategory || '',
//       price: service?.price?.toString() || '',
//       duration: service?.duration?.toString() || '',
//       priceType: service?.priceType || 'fixed',
//       bookingType: service?.bookingType || 'in_person',
//       availability: service?.availability || 'available',
//       serviceAreas: service?.serviceAreas?.length > 0 ? service.serviceAreas.map(area => ({
//         city: area.city || '',
//         state: area.state || '',
//         zipCodes: Array.isArray(area.zipCodes) ? area.zipCodes.join(', ') : '',
//         radius: area.radius?.toString() || ''
//       })) : [{
//         city: '',
//         state: '',
//         zipCodes: '',
//         radius: ''
//       }],
//       requirements: service?.requirements || [],
//       includes: service?.includes || [],
//       excludes: service?.excludes || [],
//       tags: service?.tags || [],
//       newTag: '',
//       newRequirement: '',
//       newInclude: '',
//       newExclude: ''
//     };

//     if (JSON.stringify(formData) !== JSON.stringify(originalData)) {
//       Swal.fire({
//         title: 'Unsaved Changes',
//         text: 'You have unsaved changes. Are you sure you want to close?',
//         icon: 'warning',
//         showCancelButton: true,
//         confirmButtonColor: '#3085d6',
//         cancelButtonColor: '#d33',
//         confirmButtonText: 'Yes, close'
//       }).then((result) => {
//         if (result.isConfirmed) {
//           onClose();
//         }
//       });
//     } else {
//       onClose();
//     }
//   };

//   return (
//     <Dialog 
//       open={open} 
//       onClose={handleClose}
//       maxWidth="md"
//       fullWidth
//       slotProps={{
//         paper: {
//           sx: { borderRadius: 2 }
//         }
//       }}
//     >
//       <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
//         <Typography variant="h6">
//           {service ? 'Edit Service' : 'Add New Service'}
//         </Typography>
//         <IconButton onClick={handleClose} size="small">
//           <CloseIcon />
//         </IconButton>
//       </DialogTitle>

//       <DialogContent dividers>
//         <Grid container spacing={3}>
//           <Grid item xs={12} sm={6}>
//             <TextField
//               fullWidth
//               label="Service Title"
//               name="title"
//               value={formData.title}
//               onChange={handleChange}
//               error={!!errors.title}
//               helperText={errors.title}
//               required
//             />
//           </Grid>
          
//           <Grid item xs={12} sm={6}>
//             <TextField
//               fullWidth
//               label="Service Name"
//               name="name"
//               value={formData.name}
//               onChange={handleChange}
//               error={!!errors.name}
//               helperText={errors.name}
//               required
//             />
//           </Grid>

//           <Grid item xs={12}>
//             <TextField
//               fullWidth
//               label="Description"
//               name="description"
//               value={formData.description}
//               onChange={handleChange}
//               error={!!errors.description}
//               helperText={errors.description}
//               multiline
//               rows={3}
//               required
//             />
//           </Grid>

//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth error={!!errors.category}>
//               <InputLabel>Category *</InputLabel>
//               <Select
//                 name="category"
//                 value={formData.category}
//                 onChange={handleChange}
//                 label="Category *"
//               >
//                 {SERVICE_CATEGORIES.map((category) => (
//                   <MenuItem key={category} value={category}>
//                     {category}
//                   </MenuItem>
//                 ))}
//               </Select>
//               {errors.category && (
//                 <Typography variant="caption" color="error" sx={{ ml: 2, mt: 0.5 }}>
//                   {errors.category}
//                 </Typography>
//               )}
//             </FormControl>
//           </Grid>

//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Price Type</InputLabel>
//               <Select
//                 name="priceType"
//                 value={formData.priceType}
//                 onChange={handleChange}
//                 label="Price Type"
//               >
//                 <MenuItem value="fixed">Fixed Price</MenuItem>
//                 <MenuItem value="hourly">Per Hour</MenuItem>
//                 <MenuItem value="per_project">Per Project</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           <Grid item xs={12} sm={6}>
//             <TextField
//               fullWidth
//               label="Subcategory"
//               name="subcategory"
//               value={formData.subcategory}
//               onChange={handleChange}
//               placeholder="e.g., House Cleaning, Plumbing Repair"
//             />
//           </Grid>

//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Booking Type</InputLabel>
//               <Select
//                 name="bookingType"
//                 value={formData.bookingType}
//                 onChange={handleChange}
//                 label="Booking Type"
//               >
//                 <MenuItem value="online">Online</MenuItem>
//                 <MenuItem value="in_person">In Person</MenuItem>
//                 <MenuItem value="both">Both</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Availability</InputLabel>
//               <Select
//                 name="availability"
//                 value={formData.availability}
//                 onChange={handleChange}
//                 label="Availability"
//               >
//                 <MenuItem value="available">Available</MenuItem>
//                 <MenuItem value="busy">Busy</MenuItem>
//                 <MenuItem value="unavailable">Unavailable</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           <Grid item xs={12} sm={6}>
//             <TextField
//               fullWidth
//               label="Price ($)"
//               name="price"
//               type="number"
//               value={formData.price}
//               onChange={handleChange}
//               error={!!errors.price}
//               helperText={errors.price}
//               slotProps={{
//                 input: {
//                   startAdornment: <InputAdornment position="start">$</InputAdornment>,
//                 }
//               }}
//               required
//             />
//           </Grid>

//           <Grid item xs={12} sm={6}>
//             <TextField
//               fullWidth
//               label="Duration (minutes)"
//               name="duration"
//               type="number"
//               value={formData.duration}
//               onChange={handleChange}
//               error={!!errors.duration}
//               helperText={errors.duration}
//               required
//             />
//           </Grid>

//           {/* Service Areas Section */}
//           <Grid item xs={12}>
//             <Typography variant="subtitle2" gutterBottom>
//               Service Areas *
//             </Typography>
//             {formData.serviceAreas.map((area, index) => (
//               <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
//                 <Grid item xs={12} sm={3}>
//                   <TextField
//                     fullWidth
//                     label="City"
//                     value={area.city}
//                     onChange={(e) => handleServiceAreaChange(index, 'city', e.target.value)}
//                     size="small"
//                   />
//                 </Grid>
//                 <Grid item xs={12} sm={3}>
//                   <TextField
//                     fullWidth
//                     label="State"
//                     value={area.state}
//                     onChange={(e) => handleServiceAreaChange(index, 'state', e.target.value)}
//                     size="small"
//                   />
//                 </Grid>
//                 <Grid item xs={12} sm={3}>
//                   <TextField
//                     fullWidth
//                     label="ZIP Codes (comma separated)"
//                     value={area.zipCodes}
//                     onChange={(e) => handleServiceAreaChange(index, 'zipCodes', e.target.value)}
//                     placeholder="12345, 12346"
//                     size="small"
//                   />
//                 </Grid>
//                 <Grid item xs={12} sm={3}>
//                   <TextField
//                     fullWidth
//                     label="Radius (miles)"
//                     type="number"
//                     value={area.radius}
//                     onChange={(e) => handleServiceAreaChange(index, 'radius', e.target.value)}
//                     size="small"
//                   />
//                 </Grid>
//               </Grid>
//             ))}
//             {errors.serviceAreas && (
//               <Typography variant="caption" color="error" sx={{ ml: 2 }}>
//                 {errors.serviceAreas}
//               </Typography>
//             )}
//           </Grid>

//           <Grid item xs={12}>
//             <Box>
//               <Typography variant="subtitle2" gutterBottom>
//                 Tags
//               </Typography>
//               <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
//                 {formData.tags.map((tag, index) => (
//                   <Chip
//                     key={index}
//                     label={tag}
//                     onDelete={() => handleRemoveTag(tag)}
//                     size="small"
//                     color="primary"
//                     variant="outlined"
//                   />
//                 ))}
//               </Box>
//               <TextField
//                 fullWidth
//                 label="Add Tag"
//                 name="newTag"
//                 value={formData.newTag}
//                 onChange={handleChange}
//                 onKeyDown={(e) => {
//                   if (e.key === 'Enter') {
//                     e.preventDefault();
//                     handleAddTag();
//                   }
//                 }}
//                 slotProps={{
//                   input: {
//                     endAdornment: (
//                       <InputAdornment position="end">
//                         <IconButton onClick={handleAddTag} size="small">
//                           <AddIcon />
//                         </IconButton>
//                       </InputAdornment>
//                     ),
//                   }
//                 }}
//                 placeholder="Type and press Enter or click + to add"
//               />
//             </Box>
//           </Grid>

//           {/* Requirements Section */}
//           <Grid item xs={12}>
//             <Box>
//               <Typography variant="subtitle2" gutterBottom>
//                 Requirements (What customer needs to provide)
//               </Typography>
//               <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
//                 {formData.requirements.map((requirement, index) => (
//                   <Chip
//                     key={index}
//                     label={requirement}
//                     onDelete={() => handleRemoveRequirement(requirement)}
//                     size="small"
//                     color="secondary"
//                     variant="outlined"
//                   />
//                 ))}
//               </Box>
//               <TextField
//                 fullWidth
//                 label="Add Requirement"
//                 name="newRequirement"
//                 value={formData.newRequirement}
//                 onChange={handleChange}
//                 onKeyDown={(e) => {
//                   if (e.key === 'Enter') {
//                     e.preventDefault();
//                     handleAddRequirement();
//                   }
//                 }}
//                 slotProps={{
//                   input: {
//                     endAdornment: (
//                       <InputAdornment position="end">
//                         <IconButton onClick={handleAddRequirement} size="small">
//                           <AddIcon />
//                         </IconButton>
//                       </InputAdornment>
//                     ),
//                   }
//                 }}
//                 placeholder="e.g., Access to property, Power outlet available"
//               />
//             </Box>
//           </Grid>

//           {/* Includes Section */}
//           <Grid item xs={12}>
//             <Box>
//               <Typography variant="subtitle2" gutterBottom>
//                 What's Included
//               </Typography>
//               <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
//                 {formData.includes.map((include, index) => (
//                   <Chip
//                     key={index}
//                     label={include}
//                     onDelete={() => handleRemoveInclude(include)}
//                     size="small"
//                     color="success"
//                     variant="outlined"
//                   />
//                 ))}
//               </Box>
//               <TextField
//                 fullWidth
//                 label="Add What's Included"
//                 name="newInclude"
//                 value={formData.newInclude}
//                 onChange={handleChange}
//                 onKeyDown={(e) => {
//                   if (e.key === 'Enter') {
//                     e.preventDefault();
//                     handleAddInclude();
//                   }
//                 }}
//                 slotProps={{
//                   input: {
//                     endAdornment: (
//                       <InputAdornment position="end">
//                         <IconButton onClick={handleAddInclude} size="small">
//                           <AddIcon />
//                         </IconButton>
//                       </InputAdornment>
//                     ),
//                   }
//                 }}
//                 placeholder="e.g., All cleaning supplies, Equipment provided"
//               />
//             </Box>
//           </Grid>

//           {/* Excludes Section */}
//           <Grid item xs={12}>
//             <Box>
//               <Typography variant="subtitle2" gutterBottom>
//                 What's Not Included
//               </Typography>
//               <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
//                 {formData.excludes.map((exclude, index) => (
//                   <Chip
//                     key={index}
//                     label={exclude}
//                     onDelete={() => handleRemoveExclude(exclude)}
//                     size="small"
//                     color="error"
//                     variant="outlined"
//                   />
//                 ))}
//               </Box>
//               <TextField
//                 fullWidth
//                 label="Add What's Not Included"
//                 name="newExclude"
//                 value={formData.newExclude}
//                 onChange={handleChange}
//                 onKeyDown={(e) => {
//                   if (e.key === 'Enter') {
//                     e.preventDefault();
//                     handleAddExclude();
//                   }
//                 }}
//                 slotProps={{
//                   input: {
//                     endAdornment: (
//                       <InputAdornment position="end">
//                         <IconButton onClick={handleAddExclude} size="small">
//                           <AddIcon />
//                         </IconButton>
//                       </InputAdornment>
//                     ),
//                   }
//                 }}
//                 placeholder="e.g., Travel expenses, Special equipment"
//               />
//             </Box>
//           </Grid>
//         </Grid>
//       </DialogContent>

//       <DialogActions sx={{ p: 3 }}>
//         <Button onClick={handleClose} color="inherit">
//           Cancel
//         </Button>
//         <Button 
//           onClick={handleSubmit} 
//           variant="contained" 
//           disabled={loading}
//           sx={{ minWidth: 100 }}
//         >
//           {loading ? 'Saving...' : (service ? 'Update' : 'Create')}
//         </Button>
//       </DialogActions>
//     </Dialog>
//   );
// };

// export default ServiceModal;
"use client";

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  IconButton,
  InputAdornment
} from '@mui/material';
import { Close as CloseIcon, Add as AddIcon } from '@mui/icons-material';
import Swal from 'sweetalert2';

const SERVICE_CATEGORIES = [
  'Home & Garden',
  'Health & Wellness',
  'Automotive',
  'Technology',
  'Education',
  'Events & Entertainment',
  'Business Services',
  'Personal Care',
  'Cleaning',
  'Repair & Maintenance',
  'Other'
];

const ServiceModal = ({ open, onClose, onSave, service = null, loading = false }) => {
  const [formData, setFormData] = useState({
    title: '',
    name: '',
    description: '',
    category: '',
    subcategory: '',
    price: '',
    duration: '',
    priceType: 'fixed',
    bookingType: 'in_person',
    availability: 'available',
    serviceAreas: [{
      city: '',
      state: '',
      zipCodes: '',
      radius: ''
    }],
    requirements: [],
    includes: [],
    excludes: [],
    tags: [],
    newTag: '',
    newRequirement: '',
    newInclude: '',
    newExclude: ''
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (service) {
      setFormData({
        title: service.title || '',
        name: service.name || '',
        description: service.description || '',
        category: service.category || '',
        subcategory: service.subcategory || '',
        price: service.price?.toString() || '',
        duration: service.duration?.toString() || '',
        priceType: service.priceType || 'fixed',
        bookingType: service.bookingType || 'in_person',
        availability: service.availability || 'available',
        serviceAreas: service.serviceAreas?.length > 0 ? service.serviceAreas.map(area => ({
          city: area.city || '',
          state: area.state || '',
          zipCodes: Array.isArray(area.zipCodes) ? area.zipCodes.join(', ') : '',
          radius: area.radius?.toString() || ''
        })) : [{
          city: '',
          state: '',
          zipCodes: '',
          radius: ''
        }],
        requirements: service.requirements || [],
        includes: service.includes || [],
        excludes: service.excludes || [],
        tags: service.tags || [],
        newTag: '',
        newRequirement: '',
        newInclude: '',
        newExclude: ''
      });
    } else {
      setFormData({
        title: '',
        name: '',
        description: '',
        category: '',
        subcategory: '',
        price: '',
        duration: '',
        priceType: 'fixed',
        bookingType: 'in_person',
        availability: 'available',
        serviceAreas: [{
          city: '',
          state: '',
          zipCodes: '',
          radius: ''
        }],
        requirements: [],
        includes: [],
        excludes: [],
        tags: [],
        newTag: '',
        newRequirement: '',
        newInclude: '',
        newExclude: ''
      });
    }
    setErrors({});
  }, [service, open]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleAddTag = () => {
    if (formData.newTag.trim() && !formData.tags.includes(formData.newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, prev.newTag.trim()],
        newTag: ''
      }));
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleAddRequirement = () => {
    if (formData.newRequirement.trim() && !formData.requirements.includes(formData.newRequirement.trim())) {
      setFormData(prev => ({
        ...prev,
        requirements: [...prev.requirements, prev.newRequirement.trim()],
        newRequirement: ''
      }));
    }
  };

  const handleRemoveRequirement = (itemToRemove) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter(item => item !== itemToRemove)
    }));
  };

  const handleAddInclude = () => {
    if (formData.newInclude.trim() && !formData.includes.includes(formData.newInclude.trim())) {
      setFormData(prev => ({
        ...prev,
        includes: [...prev.includes, prev.newInclude.trim()],
        newInclude: ''
      }));
    }
  };

  const handleRemoveInclude = (itemToRemove) => {
    setFormData(prev => ({
      ...prev,
      includes: prev.includes.filter(item => item !== itemToRemove)
    }));
  };

  const handleAddExclude = () => {
    if (formData.newExclude.trim() && !formData.excludes.includes(formData.newExclude.trim())) {
      setFormData(prev => ({
        ...prev,
        excludes: [...prev.excludes, prev.newExclude.trim()],
        newExclude: ''
      }));
    }
  };

  const handleRemoveExclude = (itemToRemove) => {
    setFormData(prev => ({
      ...prev,
      excludes: prev.excludes.filter(item => item !== itemToRemove)
    }));
  };

  const handleServiceAreaChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      serviceAreas: prev.serviceAreas.map((area, i) =>
        i === index ? { ...area, [field]: value } : area
      )
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.price || parseFloat(formData.price) <= 0) newErrors.price = 'Valid price is required';
    if (!formData.duration || parseInt(formData.duration) <= 0) newErrors.duration = 'Valid duration is required';

    const hasValidServiceArea = formData.serviceAreas.some(area =>
      area.city.trim() || area.state.trim()
    );
    if (!hasValidServiceArea) {
      newErrors.serviceAreas = 'At least one service area with city or state is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Please fill in all required fields correctly.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    const serviceData = {
      ...formData,
      price: parseFloat(formData.price),
      duration: parseInt(formData.duration),
      tags: formData.tags,
      requirements: formData.requirements,
      includes: formData.includes,
      excludes: formData.excludes,
      serviceAreas: formData.serviceAreas.map(area => ({
        city: area.city.trim(),
        state: area.state.trim(),
        zipCodes: area.zipCodes ? area.zipCodes.split(',').map(zip => zip.trim()).filter(zip => zip) : [],
        radius: area.radius ? parseInt(area.radius) : undefined
      })).filter(area => area.city || area.state)
    };

    delete serviceData.newTag;
    delete serviceData.newRequirement;
    delete serviceData.newInclude;
    delete serviceData.newExclude;

    try {
      await onSave(serviceData);
      onClose();
    } catch (error) {
      console.error('Error saving service:', error);
    }
  };

  const handleClose = () => {
    const originalData = {
      title: service?.title || '',
      name: service?.name || '',
      description: service?.description || '',
      category: service?.category || '',
      subcategory: service?.subcategory || '',
      price: service?.price?.toString() || '',
      duration: service?.duration?.toString() || '',
      priceType: service?.priceType || 'fixed',
      bookingType: service?.bookingType || 'in_person',
      availability: service?.availability || 'available',
      serviceAreas: service?.serviceAreas?.length > 0 ? service.serviceAreas.map(area => ({
        city: area.city || '',
        state: area.state || '',
        zipCodes: Array.isArray(area.zipCodes) ? area.zipCodes.join(', ') : '',
        radius: area.radius?.toString() || ''
      })) : [{
        city: '',
        state: '',
        zipCodes: '',
        radius: ''
      }],
      requirements: service?.requirements || [],
      includes: service?.includes || [],
      excludes: service?.excludes || [],
      tags: service?.tags || [],
      newTag: '',
      newRequirement: '',
      newInclude: '',
      newExclude: ''
    };

    if (JSON.stringify(formData) !== JSON.stringify(originalData)) {
      Swal.fire({
        title: 'Unsaved Changes',
        text: 'You have unsaved changes. Are you sure you want to close?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, close'
      }).then((result) => {
        if (result.isConfirmed) {
          onClose();
        }
      });
    } else {
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      slotProps={{
        paper: {
          className: 'rounded-2xl'
        }
      }}
    >
      <DialogTitle className="flex justify-between items-center">
        <Typography variant="h6">
          {service ? 'Edit Service' : 'Add New Service'}
        </Typography>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextField
            fullWidth
            label="Service Title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            error={!!errors.title}
            helperText={errors.title}
            required
            className="mb-4"
          />
          
          <TextField
            fullWidth
            label="Service Name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            error={!!errors.name}
            helperText={errors.name}
            required
            className="mb-4"
          />

          <TextField
            fullWidth
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            error={!!errors.description}
            helperText={errors.description}
            multiline
            rows={3}
            required
            className="col-span-2 mb-4"
          />

          <FormControl fullWidth error={!!errors.category} className="mb-4">
            <InputLabel>Category *</InputLabel>
            <Select
              name="category"
              value={formData.category}
              onChange={handleChange}
              label="Category *"
            >
              {SERVICE_CATEGORIES.map((category) => (
                <MenuItem key={category} value={category}>
                  {category}
                </MenuItem>
              ))}
            </Select>
            {errors.category && (
              <Typography variant="caption" color="error" className="mt-1 ml-2">
                {errors.category}
              </Typography>
            )}
          </FormControl>

          <TextField
            fullWidth
            label="Subcategory"
            name="subcategory"
            value={formData.subcategory}
            onChange={handleChange}
            placeholder="e.g., House Cleaning, Plumbing Repair"
            className="mb-4"
          />

          <TextField
            fullWidth
            label="Price ($)"
            name="price"
            type="number"
            value={formData.price}
            onChange={handleChange}
            error={!!errors.price}
            helperText={errors.price}
            slotProps={{
              input: {
                startAdornment: <InputAdornment position="start">$</InputAdornment>,
              }
            }}
            required
            className="mb-4"
          />

          <TextField
            fullWidth
            label="Duration (minutes)"
            name="duration"
            type="number"
            value={formData.duration}
            onChange={handleChange}
            error={!!errors.duration}
            helperText={errors.duration}
            required
            className="mb-4"
          />

          <FormControl fullWidth className="mb-4">
            <InputLabel>Price Type</InputLabel>
            <Select
              name="priceType"
              value={formData.priceType}
              onChange={handleChange}
              label="Price Type"
            >
              <MenuItem value="fixed">Fixed Price</MenuItem>
              <MenuItem value="hourly">Per Hour</MenuItem>
              <MenuItem value="per_project">Per Project</MenuItem>
            </Select>
          </FormControl>

          <FormControl fullWidth className="mb-4">
            <InputLabel>Booking Type</InputLabel>
            <Select
              name="bookingType"
              value={formData.bookingType}
              onChange={handleChange}
              label="Booking Type"
            >
              <MenuItem value="online">Online</MenuItem>
              <MenuItem value="in_person">In Person</MenuItem>
              <MenuItem value="both">Both</MenuItem>
            </Select>
          </FormControl>

          <FormControl fullWidth className="mb-4">
            <InputLabel>Availability</InputLabel>
            <Select
              name="availability"
              value={formData.availability}
              onChange={handleChange}
              label="Availability"
            >
              <MenuItem value="available">Available</MenuItem>
              <MenuItem value="busy">Busy</MenuItem>
              <MenuItem value="unavailable">Unavailable</MenuItem>
            </Select>
          </FormControl>

          <div className="col-span-2">
            <Typography variant="subtitle2" gutterBottom>
              Service Areas *
            </Typography>
            {formData.serviceAreas.map((area, index) => (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4" key={index}>
                <TextField
                  fullWidth
                  label="City"
                  value={area.city}
                  onChange={(e) => handleServiceAreaChange(index, 'city', e.target.value)}
                  size="small"
                />
                <TextField
                  fullWidth
                  label="State"
                  value={area.state}
                  onChange={(e) => handleServiceAreaChange(index, 'state', e.target.value)}
                  size="small"
                />
                <TextField
                  fullWidth
                  label="ZIP Codes (comma separated)"
                  value={area.zipCodes}
                  onChange={(e) => handleServiceAreaChange(index, 'zipCodes', e.target.value)}
                  placeholder="12345, 12346"
                  size="small"
                />
                <TextField
                  fullWidth
                  label="Radius (miles)"
                  type="number"
                  value={area.radius}
                  onChange={(e) => handleServiceAreaChange(index, 'radius', e.target.value)}
                  size="small"
                />
              </div>
            ))}
            {errors.serviceAreas && (
              <Typography variant="caption" color="error" className="ml-2">
                {errors.serviceAreas}
              </Typography>
            )}
          </div>

          <div className="col-span-2">
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Tags
              </Typography>
              <Box className="flex flex-wrap gap-2 mb-4">
                {formData.tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    onDelete={() => handleRemoveTag(tag)}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
              <TextField
                fullWidth
                label="Add Tag"
                name="newTag"
                value={formData.newTag}
                onChange={handleChange}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleAddTag} size="small">
                          <AddIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
                placeholder="Type and press Enter or click + to add"
              />
            </Box>
          </div>

          <div className="col-span-2">
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Requirements (What customer needs to provide)
              </Typography>
              <Box className="flex flex-wrap gap-2 mb-4">
                {formData.requirements.map((requirement, index) => (
                  <Chip
                    key={index}
                    label={requirement}
                    onDelete={() => handleRemoveRequirement(requirement)}
                    size="small"
                    color="secondary"
                    variant="outlined"
                  />
                ))}
              </Box>
              <TextField
                fullWidth
                label="Add Requirement"
                name="newRequirement"
                value={formData.newRequirement}
                onChange={handleChange}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddRequirement();
                  }
                }}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleAddRequirement} size="small">
                          <AddIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
                placeholder="e.g., Access to property, Power outlet available"
              />
            </Box>
          </div>

          <div className="col-span-2">
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                What's Included
              </Typography>
              <Box className="flex flex-wrap gap-2 mb-4">
                {formData.includes.map((include, index) => (
                  <Chip
                    key={index}
                    label={include}
                    onDelete={() => handleRemoveInclude(include)}
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                ))}
              </Box>
              <TextField
                fullWidth
                label="Add What's Included"
                name="newInclude"
                value={formData.newInclude}
                onChange={handleChange}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddInclude();
                  }
                }}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleAddInclude} size="small">
                          <AddIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
                placeholder="e.g., All cleaning supplies, Equipment provided"
              />
            </Box>
          </div>

          <div className="col-span-2">
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                What's Not Included
              </Typography>
              <Box className="flex flex-wrap gap-2 mb-4">
                {formData.excludes.map((exclude, index) => (
                  <Chip
                    key={index}
                    label={exclude}
                    onDelete={() => handleRemoveExclude(exclude)}
                    size="small"
                    color="error"
                    variant="outlined"
                  />
                ))}
              </Box>
              <TextField
                fullWidth
                label="Add What's Not Included"
                name="newExclude"
                value={formData.newExclude}
                onChange={handleChange}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddExclude();
                  }
                }}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleAddExclude} size="small">
                          <AddIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
                placeholder="e.g., Travel expenses, Special equipment"
              />
            </Box>
          </div>
        </div>
      </DialogContent>

      <DialogActions className="p-6">
        <Button onClick={handleClose} color="inherit">
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          disabled={loading}
          className="min-w-[100px]"
        >
          {loading ? 'Saving...' : (service ? 'Update' : 'Create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ServiceModal;