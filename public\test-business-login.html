<!DOCTYPE html>
<html>
<head>
    <title>Test Business Owner Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        div { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        input { margin: 5px; padding: 8px; width: 200px; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Test Business Owner Login</h1>
    
    <div>
        <h2>1. Create Test Users First</h2>
        <button onclick="createTestUsers()">Create Test Users</button>
        <div id="createResult"></div>
    </div>
    
    <div>
        <h2>2. Test Business Owner Login</h2>
        <input type="email" id="email" value="<EMAIL>" placeholder="Email">
        <input type="password" id="password" value="password" placeholder="Password">
        <button onclick="testBusinessLogin()">Test Business Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div>
        <h2>3. Test Regular User Login</h2>
        <input type="email" id="userEmail" value="<EMAIL>" placeholder="Email">
        <input type="password" id="userPassword" value="password" placeholder="Password">
        <button onclick="testUserLogin()">Test User Login</button>
        <div id="userLoginResult"></div>
    </div>

    <script>
        async function createTestUsers() {
            try {
                document.getElementById('createResult').innerHTML = 'Creating test users...';
                
                const response = await fetch('/api/test-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const result = await response.json();
                document.getElementById('createResult').innerHTML = 
                    '<div class="success">✅ Success!</div><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                console.log('Create users result:', result);
            } catch (error) {
                document.getElementById('createResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
                console.error('Create users error:', error);
            }
        }
        
        async function testBusinessLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                document.getElementById('loginResult').innerHTML = 'Testing business owner login...';
                console.log('Testing business owner login with:', { email, password: '***' });
                
                const response = await fetch('/api/auth/businessOwnerLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const result = await response.json();
                const statusClass = response.ok ? 'success' : 'error';
                const statusIcon = response.ok ? '✅' : '❌';
                
                document.getElementById('loginResult').innerHTML = 
                    `<div class="${statusClass}">${statusIcon} Status: ${response.status}</div><pre>` + JSON.stringify(result, null, 2) + '</pre>';
                console.log('Business login result:', result);
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
                console.error('Business login error:', error);
            }
        }
        
        async function testUserLogin() {
            const email = document.getElementById('userEmail').value;
            const password = document.getElementById('userPassword').value;
            
            try {
                document.getElementById('userLoginResult').innerHTML = 'Testing user login...';
                console.log('Testing user login with:', { email, password: '***' });
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const result = await response.json();
                const statusClass = response.ok ? 'success' : 'error';
                const statusIcon = response.ok ? '✅' : '❌';
                
                document.getElementById('userLoginResult').innerHTML = 
                    `<div class="${statusClass}">${statusIcon} Status: ${response.status}</div><pre>` + JSON.stringify(result, null, 2) + '</pre>';
                console.log('User login result:', result);
            } catch (error) {
                document.getElementById('userLoginResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
                console.error('User login error:', error);
            }
        }
    </script>
</body>
</html>
