"use client";

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  Typography,
  IconButton,
  Tabs,
  Tab
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import Swal from 'sweetalert2';

const EMPLOYMENT_TYPES = ['full-time', 'part-time', 'contract', 'freelance'];
const EMPLOYEE_STATUSES = ['active', 'inactive', 'suspended'];

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`employee-tabpanel-${index}`}
      aria-labelledby={`employee-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

const EmployeeModal = ({ open, onClose, onSave, employee = null, loading = false }) => {
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    // Basic Information
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    password: '',
    
    // Employment Details
    position: '',
    department: '',
    employmentType: 'full-time',
    status: 'active',
    
    // Address
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    },
    
    // Emergency Contact
    emergencyContact: {
      name: '',
      relationship: '',
      phoneNumber: ''
    },
    
    // Compensation
    compensation: {
      type: 'hourly',
      amount: '',
      currency: 'USD'
    },
    
    // Additional Info
    bio: '',
    languages: []
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (employee) {
      // Edit mode - populate form with employee data
      setFormData({
        firstName: employee.firstName || '',
        lastName: employee.lastName || '',
        email: employee.email || '',
        phoneNumber: employee.phoneNumber || '',
        password: '', // Don't populate password for security
        position: employee.position || '',
        department: employee.department || '',
        employmentType: employee.employmentType || 'full-time',
        status: employee.status || 'active',
        address: {
          street: employee.address?.street || '',
          city: employee.address?.city || '',
          state: employee.address?.state || '',
          zipCode: employee.address?.zipCode || '',
          country: employee.address?.country || 'USA'
        },
        emergencyContact: {
          name: employee.emergencyContact?.name || '',
          relationship: employee.emergencyContact?.relationship || '',
          phoneNumber: employee.emergencyContact?.phoneNumber || ''
        },
        compensation: {
          type: employee.compensation?.type || 'hourly',
          amount: employee.compensation?.amount?.toString() || '',
          currency: employee.compensation?.currency || 'USD'
        },
        bio: employee.bio || '',
        languages: employee.languages || []
      });
    } else {
      // Add mode - reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        password: '',
        position: '',
        department: '',
        employmentType: 'full-time',
        status: 'active',
        address: {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'USA'
        },
        emergencyContact: {
          name: '',
          relationship: '',
          phoneNumber: ''
        },
        compensation: {
          type: 'hourly',
          amount: '',
          currency: 'USD'
        },
        bio: '',
        languages: []
      });
    }
    setErrors({});
    setTabValue(0);
  }, [employee, open]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      // Handle nested objects
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Basic Information Validation
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
    if (!formData.position.trim()) newErrors.position = 'Position is required';
    
    // Password validation for new employees
    if (!employee && !formData.password.trim()) {
      newErrors.password = 'Password is required for new employees';
    }
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    // Compensation validation
    if (formData.compensation.amount && isNaN(parseFloat(formData.compensation.amount))) {
      newErrors['compensation.amount'] = 'Please enter a valid amount';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Please fill in all required fields correctly.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    const employeeData = {
      ...formData,
      compensation: {
        ...formData.compensation,
        amount: formData.compensation.amount ? parseFloat(formData.compensation.amount) : undefined
      }
    };

    // Remove empty password for updates
    if (employee && !employeeData.password) {
      delete employeeData.password;
    }

    try {
      await onSave(employeeData);
      onClose();
    } catch (error) {
      console.error('Error saving employee:', error);
    }
  };

  const handleClose = () => {
    // Check for unsaved changes
    const hasChanges = JSON.stringify(formData) !== JSON.stringify({
      firstName: employee?.firstName || '',
      lastName: employee?.lastName || '',
      email: employee?.email || '',
      phoneNumber: employee?.phoneNumber || '',
      password: '',
      position: employee?.position || '',
      department: employee?.department || '',
      employmentType: employee?.employmentType || 'full-time',
      status: employee?.status || 'active',
      address: {
        street: employee?.address?.street || '',
        city: employee?.address?.city || '',
        state: employee?.address?.state || '',
        zipCode: employee?.address?.zipCode || '',
        country: employee?.address?.country || 'USA'
      },
      emergencyContact: {
        name: employee?.emergencyContact?.name || '',
        relationship: employee?.emergencyContact?.relationship || '',
        phoneNumber: employee?.emergencyContact?.phoneNumber || ''
      },
      compensation: {
        type: employee?.compensation?.type || 'hourly',
        amount: employee?.compensation?.amount?.toString() || '',
        currency: employee?.compensation?.currency || 'USD'
      },
      bio: employee?.bio || '',
      languages: employee?.languages || []
    });

    if (hasChanges) {
      Swal.fire({
        title: 'Unsaved Changes',
        text: 'You have unsaved changes. Are you sure you want to close?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, close'
      }).then((result) => {
        if (result.isConfirmed) {
          onClose();
        }
      });
    } else {
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: 2, maxHeight: '90vh' }
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          {employee ? 'Edit Employee' : 'Add New Employee'}
        </Typography>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="Basic Info" />
            <Tab label="Employment" />
            <Tab label="Contact & Address" />
            <Tab label="Additional Info" />
          </Tabs>
        </Box>

        {/* Basic Information Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                error={!!errors.firstName}
                helperText={errors.firstName}
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                error={!!errors.lastName}
                helperText={errors.lastName}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                error={!!errors.email}
                helperText={errors.email}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                error={!!errors.phoneNumber}
                helperText={errors.phoneNumber}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label={employee ? "New Password (leave blank to keep current)" : "Password"}
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                error={!!errors.password}
                helperText={errors.password || (employee ? "Leave blank to keep current password" : "")}
                required={!employee}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Employment Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Position/Job Title"
                name="position"
                value={formData.position}
                onChange={handleChange}
                error={!!errors.position}
                helperText={errors.position}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Department"
                name="department"
                value={formData.department}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Employment Type</InputLabel>
                <Select
                  name="employmentType"
                  value={formData.employmentType}
                  onChange={handleChange}
                  label="Employment Type"
                >
                  {EMPLOYMENT_TYPES.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label="Status"
                >
                  {EMPLOYEE_STATUSES.map((status) => (
                    <MenuItem key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>Compensation Type</InputLabel>
                <Select
                  name="compensation.type"
                  value={formData.compensation.type}
                  onChange={handleChange}
                  label="Compensation Type"
                >
                  <MenuItem value="hourly">Hourly</MenuItem>
                  <MenuItem value="salary">Salary</MenuItem>
                  <MenuItem value="commission">Commission</MenuItem>
                  <MenuItem value="per-service">Per Service</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Amount"
                name="compensation.amount"
                type="number"
                value={formData.compensation.amount}
                onChange={handleChange}
                error={!!errors['compensation.amount']}
                helperText={errors['compensation.amount']}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Currency"
                name="compensation.currency"
                value={formData.compensation.currency}
                onChange={handleChange}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Contact & Address Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Address
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Street Address"
                name="address.street"
                value={formData.address.street}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="City"
                name="address.city"
                value={formData.address.city}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="State"
                name="address.state"
                value={formData.address.state}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="ZIP Code"
                name="address.zipCode"
                value={formData.address.zipCode}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Emergency Contact
              </Typography>
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Contact Name"
                name="emergencyContact.name"
                value={formData.emergencyContact.name}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Relationship"
                name="emergencyContact.relationship"
                value={formData.emergencyContact.relationship}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Phone Number"
                name="emergencyContact.phoneNumber"
                value={formData.emergencyContact.phoneNumber}
                onChange={handleChange}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Additional Info Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Bio/Description"
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                multiline
                rows={4}
                placeholder="Brief description about the employee..."
              />
            </Grid>
          </Grid>
        </TabPanel>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} color="inherit">
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          disabled={loading}
          sx={{ minWidth: 100 }}
        >
          {loading ? 'Saving...' : (employee ? 'Update' : 'Create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmployeeModal;
