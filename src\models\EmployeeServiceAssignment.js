import mongoose from 'mongoose';

const employeeServiceAssignmentSchema = new mongoose.Schema({
  // Core Relationships
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: [true, 'Employee is required']
  },
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: [true, 'Service is required']
  },
  businessOwner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Business owner is required']
  },
  
  // Assignment Details
  assignedDate: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Role & Specialization
  role: {
    type: String,
    enum: ['primary', 'secondary', 'backup', 'trainee'],
    default: 'primary'
  },
  specialization: {
    type: String,
    trim: true
  },
  
  // Permissions & Capabilities
  permissions: {
    canAcceptBookings: { type: Boolean, default: true },
    canModifyService: { type: Boolean, default: false },
    canViewAnalytics: { type: Boolean, default: false },
    canManageSchedule: { type: Boolean, default: true }
  },
  
  // Performance Tracking
  performanceData: {
    bookingsHandled: { type: Number, default: 0 },
    bookingsCompleted: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalEarnings: { type: Number, default: 0 },
    lastBookingDate: Date
  },
  
  // Availability for this specific service
  serviceAvailability: {
    maxBookingsPerDay: { type: Number, default: 5 },
    maxBookingsPerWeek: { type: Number, default: 25 },
    preferredTimeSlots: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
      },
      startTime: String,
      endTime: String
    }]
  },
  
  // Training & Certification Status
  trainingStatus: {
    isCompleted: { type: Boolean, default: false },
    completedDate: Date,
    trainedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee'
    },
    certificationLevel: {
      type: String,
      enum: ['trainee', 'certified', 'expert', 'trainer'],
      default: 'trainee'
    }
  },
  
  // Notes & Comments
  notes: String,
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Deactivation Info
  deactivatedDate: Date,
  deactivatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  deactivationReason: String
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance
employeeServiceAssignmentSchema.index({ employee: 1, service: 1 }, { unique: true });
employeeServiceAssignmentSchema.index({ businessOwner: 1, isActive: 1 });
employeeServiceAssignmentSchema.index({ service: 1, isActive: 1 });
employeeServiceAssignmentSchema.index({ employee: 1, isActive: 1 });

// Virtual for completion rate
employeeServiceAssignmentSchema.virtual('completionRate').get(function() {
  if (this.performanceData.bookingsHandled === 0) return 0;
  return (this.performanceData.bookingsCompleted / this.performanceData.bookingsHandled * 100).toFixed(2);
});

// Static methods
employeeServiceAssignmentSchema.statics.getActiveAssignments = function(businessOwnerId) {
  return this.find({ businessOwner: businessOwnerId, isActive: true })
    .populate('employee', 'firstName lastName email phoneNumber status')
    .populate('service', 'title name category status');
};

employeeServiceAssignmentSchema.statics.getEmployeeServices = function(employeeId) {
  return this.find({ employee: employeeId, isActive: true })
    .populate('service', 'title name category price duration status');
};

employeeServiceAssignmentSchema.statics.getServiceEmployees = function(serviceId) {
  return this.find({ service: serviceId, isActive: true })
    .populate('employee', 'firstName lastName email phoneNumber status performanceMetrics')
    .sort({ role: 1, 'performanceData.averageRating': -1 });
};

// Instance methods
employeeServiceAssignmentSchema.methods.updatePerformance = function(bookingData) {
  const perf = this.performanceData;
  
  perf.bookingsHandled += 1;
  perf.lastBookingDate = new Date();
  
  if (bookingData.status === 'completed') {
    perf.bookingsCompleted += 1;
    
    if (bookingData.rating) {
      const totalRatingPoints = perf.averageRating * (perf.bookingsCompleted - 1);
      perf.averageRating = (totalRatingPoints + bookingData.rating) / perf.bookingsCompleted;
    }
    
    if (bookingData.earnings) {
      perf.totalEarnings += bookingData.earnings;
    }
  }
  
  return this.save();
};

employeeServiceAssignmentSchema.methods.deactivate = function(reason, deactivatedBy) {
  this.isActive = false;
  this.deactivatedDate = new Date();
  this.deactivatedBy = deactivatedBy;
  this.deactivationReason = reason;
  return this.save();
};

employeeServiceAssignmentSchema.methods.reactivate = function() {
  this.isActive = true;
  this.deactivatedDate = undefined;
  this.deactivatedBy = undefined;
  this.deactivationReason = undefined;
  return this.save();
};

const EmployeeServiceAssignment = mongoose.models.EmployeeServiceAssignment || 
  mongoose.model('EmployeeServiceAssignment', employeeServiceAssignmentSchema);

export default EmployeeServiceAssignment;
