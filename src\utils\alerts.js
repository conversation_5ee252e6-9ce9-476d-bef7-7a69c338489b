import Swal from 'sweetalert2';

// Custom SweetAlert2 configurations
const swalConfig = {
  confirmButtonColor: '#3085d6',
  cancelButtonColor: '#d33',
  background: '#fff',
  color: '#333',
  borderRadius: '8px'
};

// Success alert
export const showSuccessAlert = (title, text = '') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'success',
    title,
    text,
    timer: 3000,
    timerProgressBar: true,
    showConfirmButton: false
  });
};

// Error alert
export const showErrorAlert = (title, text = '') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'error',
    title,
    text,
    confirmButtonText: 'OK'
  });
};

// Warning alert
export const showWarningAlert = (title, text = '') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'warning',
    title,
    text,
    confirmButtonText: 'OK'
  });
};

// Info alert
export const showInfoAlert = (title, text = '') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'info',
    title,
    text,
    confirmButtonText: 'OK'
  });
};

// Confirmation dialog
export const showConfirmDialog = (title, text, confirmText = 'Yes', cancelText = 'Cancel') => {
  return Swal.fire({
    ...swalConfig,
    title,
    text,
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: confirmText,
    cancelButtonText: cancelText
  });
};

// Delete confirmation dialog
export const showDeleteConfirmation = (itemName = 'this item') => {
  return Swal.fire({
    ...swalConfig,
    title: 'Are you sure?',
    text: `You won't be able to revert this! This will permanently delete ${itemName}.`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, delete it!',
    cancelButtonText: 'Cancel',
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6'
  });
};

// Loading alert
export const showLoadingAlert = (title = 'Processing...', text = 'Please wait') => {
  return Swal.fire({
    ...swalConfig,
    title,
    text,
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });
};

// Close loading alert
export const closeLoadingAlert = () => {
  Swal.close();
};

// Service-specific alerts
export const serviceAlerts = {
  // Service created successfully
  created: (serviceName) => showSuccessAlert(
    'Service Created!',
    `${serviceName} has been created successfully.`
  ),

  // Service updated successfully
  updated: (serviceName) => showSuccessAlert(
    'Service Updated!',
    `${serviceName} has been updated successfully.`
  ),

  // Service deleted successfully
  deleted: (serviceName) => showSuccessAlert(
    'Service Deleted!',
    `${serviceName} has been deleted successfully.`
  ),

  // Service status toggled
  statusToggled: (serviceName, newStatus) => showSuccessAlert(
    'Status Updated!',
    `${serviceName} is now ${newStatus}.`
  ),

  // Confirm service deletion
  confirmDelete: (serviceName) => showDeleteConfirmation(`the service "${serviceName}"`),

  // Confirm status toggle
  confirmStatusToggle: (serviceName, currentStatus) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    return showConfirmDialog(
      'Change Service Status',
      `Are you sure you want to make "${serviceName}" ${newStatus}?`,
      `Yes, make it ${newStatus}`,
      'Cancel'
    );
  },

  // Error creating service
  createError: (error = 'Unknown error occurred') => showErrorAlert(
    'Failed to Create Service',
    `Error: ${error}`
  ),

  // Error updating service
  updateError: (error = 'Unknown error occurred') => showErrorAlert(
    'Failed to Update Service',
    `Error: ${error}`
  ),

  // Error deleting service
  deleteError: (error = 'Unknown error occurred') => showErrorAlert(
    'Failed to Delete Service',
    `Error: ${error}`
  ),

  // Error loading services
  loadError: () => showErrorAlert(
    'Failed to Load Services',
    'There was an error loading your services. Please try again.'
  ),

  // No services found
  noServices: () => showInfoAlert(
    'No Services Found',
    'You haven\'t created any services yet. Click "Add Service" to get started!'
  ),

  // Validation error
  validationError: (message = 'Please check your input and try again.') => showErrorAlert(
    'Validation Error',
    message
  )
};

// Booking-specific alerts (for future use)
export const bookingAlerts = {
  confirmed: (bookingId) => showSuccessAlert(
    'Booking Confirmed!',
    `Booking #${bookingId} has been confirmed.`
  ),

  cancelled: (bookingId) => showSuccessAlert(
    'Booking Cancelled',
    `Booking #${bookingId} has been cancelled.`
  ),

  confirmCancel: (bookingId) => showConfirmDialog(
    'Cancel Booking',
    `Are you sure you want to cancel booking #${bookingId}?`,
    'Yes, cancel it',
    'Keep booking'
  )
};

// General utility alerts
export const generalAlerts = {
  networkError: () => showErrorAlert(
    'Network Error',
    'Please check your internet connection and try again.'
  ),

  unauthorized: () => showErrorAlert(
    'Unauthorized',
    'You don\'t have permission to perform this action.'
  ),

  sessionExpired: () => showErrorAlert(
    'Session Expired',
    'Your session has expired. Please log in again.'
  ),

  comingSoon: () => showInfoAlert(
    'Coming Soon',
    'This feature is coming soon!'
  )
};
