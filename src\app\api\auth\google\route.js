import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // For now, redirect to a placeholder or show a message
    // In a real implementation, you would integrate with Google OAuth
    const googleAuthUrl = `https://accounts.google.com/oauth/authorize?` +
      `client_id=${process.env.GOOGLE_CLIENT_ID}&` +
      `redirect_uri=${process.env.NEXT_PUBLIC_BACKEND_URL}/api/auth/google/callback&` +
      `scope=openid%20profile%20email&` +
      `response_type=code&` +
      `access_type=offline&` +
      `prompt=consent`;

    return NextResponse.redirect(googleAuthUrl);
  } catch (error) {
    console.error('Google OAuth error:', error);
    return NextResponse.json({
      success: false,
      message: 'Google OAuth not configured'
    }, { status: 500 });
  }
}

export async function POST() {
  return NextResponse.json({
    success: false,
    message: 'Method not allowed'
  }, { status: 405 });
}
