import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';

export async function GET(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const businessOwnerId = decoded.userId;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const status = searchParams.get('status');
    const skip = (page - 1) * limit;
    
    // Build query for business owner's services
    let query = { businessOwner: businessOwnerId };
    
    if (status && status !== 'all') {
      query.status = status;
    }
    
    // Get services with booking counts
    const services = await Service.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    // Get booking counts for each service
    const servicesWithBookings = await Promise.all(
      services.map(async (service) => {
        const bookingsCount = await Booking.countDocuments({ 
          service: service._id 
        });
        
        return {
          ...service.toObject(),
          bookingsCount
        };
      })
    );
    
    const total = await Service.countDocuments(query);
    
    return apiResponse.success({
      services: servicesWithBookings,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }, 'Services fetched successfully');
    
  } catch (error) {
    console.error('Error fetching business services:', error);
    return apiResponse.error('Failed to fetch services', 500);
  }
}

export async function POST(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const businessOwnerId = decoded.userId;
    const serviceData = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'category', 'price'];
    for (const field of requiredFields) {
      if (!serviceData[field]) {
        return apiResponse.error(`${field} is required`, 400);
      }
    }
    
    // Create new service
    const service = new Service({
      ...serviceData,
      businessOwner: businessOwnerId,
      status: 'active' // Default status
    });
    
    await service.save();
    
    // Populate business owner details
    await service.populate('businessOwner', 'businessName ownerFirstName ownerLastName email');
    
    return apiResponse.success(service, 'Service created successfully', 201);
    
  } catch (error) {
    console.error('Error creating service:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    return apiResponse.error('Failed to create service', 500);
  }
}
