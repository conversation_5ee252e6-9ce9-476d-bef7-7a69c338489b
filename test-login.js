const fetch = require('node-fetch');

async function testLogin() {
  const baseUrl = 'http://localhost:3000';
  
  try {
    // First create test users
    console.log('Creating test users...');
    const createResponse = await fetch(`${baseUrl}/api/test-users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    const createResult = await createResponse.json();
    console.log('Create users result:', createResult);
    
    // Test business owner login
    console.log('\nTesting business owner login...');
    const loginResponse = await fetch(`${baseUrl}/api/auth/businessOwnerLogin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
      }),
    });
    
    const loginResult = await loginResponse.json();
    console.log('Business owner login result:', loginResult);
    
    // Test regular user login
    console.log('\nTesting regular user login...');
    const userLoginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
      }),
    });
    
    const userLoginResult = await userLoginResponse.json();
    console.log('User login result:', userLoginResult);
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testLogin();
