import nodemailer from 'nodemailer';

// Create reusable transporter object using SMTP transport
const createTransporter = () => {
  // For development, you can use a service like Ethereal Email for testing
  // For production, use a real email service like Gmail, SendGrid, etc.
  
  if (process.env.NODE_ENV === 'development') {
    // Development configuration - you can use Ethereal Email or any test service
    return nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.ethereal.email',
      port: process.env.SMTP_PORT || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER || '<EMAIL>',
        pass: process.env.SMTP_PASS || 'ethereal.pass'
      }
    });
  } else {
    // Production configuration
    if (process.env.EMAIL_SERVICE === 'gmail') {
      return nodemailer.createTransporter({
        service: 'gmail',
        auth: {
          user: process.env.GMAIL_USER,
          pass: process.env.GMAIL_APP_PASSWORD // Use App Password, not regular password
        }
      });
    } else if (process.env.EMAIL_SERVICE === 'sendgrid') {
      return nodemailer.createTransporter({
        host: 'smtp.sendgrid.net',
        port: 587,
        secure: false,
        auth: {
          user: 'apikey',
          pass: process.env.SENDGRID_API_KEY
        }
      });
    } else {
      // Generic SMTP configuration
      return nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });
    }
  }
};

export const sendEmail = async ({ to, subject, text, html }) => {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: {
        name: process.env.FROM_NAME || 'BookMyService',
        address: process.env.FROM_EMAIL || '<EMAIL>'
      },
      to,
      subject,
      text,
      html: html || text
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', info.messageId);
    
    // For development, log the preview URL
    if (process.env.NODE_ENV === 'development') {
      console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
    }
    
    return {
      success: true,
      messageId: info.messageId,
      previewUrl: process.env.NODE_ENV === 'development' ? nodemailer.getTestMessageUrl(info) : null
    };
    
  } catch (error) {
    console.error('Error sending email:', error);
    throw new Error(`Failed to send email: ${error.message}`);
  }
};

// Email templates
export const emailTemplates = {
  welcome: (name) => ({
    subject: 'Welcome to BookMyService!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome to BookMyService!</h1>
        <p>Dear ${name},</p>
        <p>Thank you for joining BookMyService! We're excited to have you as part of our community.</p>
        <p>You can now:</p>
        <ul>
          <li>Browse and book amazing services</li>
          <li>Connect with trusted service providers</li>
          <li>Manage your bookings easily</li>
        </ul>
        <p>Get started by exploring our services!</p>
        <p>Best regards,<br>The BookMyService Team</p>
      </div>
    `
  }),
  
  emailVerification: (name, verificationLink) => ({
    subject: 'Verify Your Email - BookMyService',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Verify Your Email</h1>
        <p>Dear ${name},</p>
        <p>Please click the button below to verify your email address:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationLink}" 
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Verify Email
          </a>
        </div>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all;">${verificationLink}</p>
        <p>This link will expire in 24 hours.</p>
        <p>Best regards,<br>The BookMyService Team</p>
      </div>
    `
  }),
  
  passwordReset: (name, resetLink) => ({
    subject: 'Reset Your Password - BookMyService',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Reset Your Password</h1>
        <p>Dear ${name},</p>
        <p>You requested to reset your password. Click the button below to set a new password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetLink}" 
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Reset Password
          </a>
        </div>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all;">${resetLink}</p>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this password reset, please ignore this email.</p>
        <p>Best regards,<br>The BookMyService Team</p>
      </div>
    `
  }),
  
  bookingConfirmation: (customerName, serviceName, providerName, date, time) => ({
    subject: `Booking Confirmed - ${serviceName}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #10b981;">Booking Confirmed!</h1>
        <p>Dear ${customerName},</p>
        <p>Your booking has been confirmed!</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Booking Details:</h3>
          <p><strong>Service:</strong> ${serviceName}</p>
          <p><strong>Provider:</strong> ${providerName}</p>
          <p><strong>Date:</strong> ${date}</p>
          <p><strong>Time:</strong> ${time}</p>
        </div>
        
        <p>The service provider will contact you soon to finalize any additional details.</p>
        <p>You can view and manage your bookings in your dashboard.</p>
        
        <p>Best regards,<br>The BookMyService Team</p>
      </div>
    `
  })
};

// Utility function to send templated emails
export const sendTemplatedEmail = async (template, to, data) => {
  try {
    const emailContent = emailTemplates[template](data);
    return await sendEmail({
      to,
      subject: emailContent.subject,
      html: emailContent.html
    });
  } catch (error) {
    console.error(`Error sending ${template} email:`, error);
    throw error;
  }
};
