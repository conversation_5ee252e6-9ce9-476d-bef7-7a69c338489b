import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';

export async function GET(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded) {
      return apiResponse.error('Invalid token', 401);
    }

    // Handle both token structures (user.role and businessOwner.role)
    let businessOwnerId;
    let userRole;

    if (decoded.user) {
      // Standard user token structure
      businessOwnerId = decoded.user.id;
      userRole = decoded.user.role;
    } else if (decoded.businessOwner) {
      // Business owner token structure
      businessOwnerId = decoded.businessOwner.id;
      userRole = decoded.businessOwner.role;
    } else {
      return apiResponse.error('Invalid token structure', 401);
    }

    if (userRole !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    // businessOwnerId is already set above in the token verification logic
    
    // Get current date for monthly calculations
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    // Parallel queries for better performance
    const [
      totalServices,
      activeBookings,
      monthlyBookings,
      completedBookings,
      allBookings
    ] = await Promise.all([
      // Total services count
      Service.countDocuments({ businessOwner: businessOwnerId, isActive: true }),
      
      // Active bookings (confirmed, in_progress)
      Booking.countDocuments({ 
        businessOwner: businessOwnerId, 
        status: { $in: ['confirmed', 'in_progress'] }
      }),
      
      // Monthly bookings for revenue calculation
      Booking.find({ 
        businessOwner: businessOwnerId,
        status: 'completed',
        createdAt: { $gte: startOfMonth, $lte: endOfMonth }
      }).populate('service', 'price'),
      
      // Completed bookings for rating calculation
      Booking.find({ 
        businessOwner: businessOwnerId,
        status: 'completed',
        rating: { $exists: true, $ne: null }
      }).select('rating'),
      
      // All bookings for additional stats
      Booking.countDocuments({ businessOwner: businessOwnerId })
    ]);
    
    // Calculate monthly revenue
    const monthlyRevenue = monthlyBookings.reduce((total, booking) => {
      return total + (booking.service?.price || 0);
    }, 0);
    
    // Calculate average customer rating
    const totalRating = completedBookings.reduce((sum, booking) => sum + booking.rating, 0);
    const averageRating = completedBookings.length > 0 ? totalRating / completedBookings.length : 0;
    
    // Get pending bookings count
    const pendingBookings = await Booking.countDocuments({ 
      businessOwner: businessOwnerId, 
      status: 'pending' 
    });
    
    // Get completed bookings count
    const completedBookingsCount = await Booking.countDocuments({ 
      businessOwner: businessOwnerId, 
      status: 'completed' 
    });
    
    // Calculate growth metrics (compared to previous month)
    const prevMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const prevMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
    
    const prevMonthBookings = await Booking.find({ 
      businessOwner: businessOwnerId,
      status: 'completed',
      createdAt: { $gte: prevMonthStart, $lte: prevMonthEnd }
    }).populate('service', 'price');
    
    const prevMonthRevenue = prevMonthBookings.reduce((total, booking) => {
      return total + (booking.service?.price || 0);
    }, 0);
    
    const revenueGrowth = prevMonthRevenue > 0 
      ? ((monthlyRevenue - prevMonthRevenue) / prevMonthRevenue) * 100 
      : 0;
    
    const stats = {
      totalServices,
      activeBookings,
      monthlyRevenue,
      customerRating: Number(averageRating.toFixed(1)),
      pendingBookings,
      completedBookings: completedBookingsCount,
      totalBookings: allBookings,
      revenueGrowth: Number(revenueGrowth.toFixed(1)),
      monthlyBookingsCount: monthlyBookings.length,
      prevMonthRevenue
    };
    
    return apiResponse.success(stats, 'Dashboard stats fetched successfully');
    
  } catch (error) {
    console.error('Error fetching business dashboard stats:', error);
    return apiResponse.error('Failed to fetch dashboard stats', 500);
  }
}
