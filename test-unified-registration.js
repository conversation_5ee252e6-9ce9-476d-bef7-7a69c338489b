// Test script for unified business owner registration
const fetch = require('node-fetch');

const testUnifiedRegistration = async () => {
  const testData = {
    email: "<EMAIL>",
    password: "password123",
    phoneNumber: "1234567890",
    ownerFirstName: "<PERSON>",
    ownerLastName: "Business",
    businessName: "Jane's Test Business",
    businessCategory: "Cleaning",
    businessDescription: "Professional cleaning services for homes and offices",
    businessAddress: "456 Business Avenue",
    city: "Los Angeles",
    state: "CA",
    zipCode: "90001",
    country: "USA",
    role: "business_owner"
  };

  try {
    console.log('Testing Unified Business Owner Registration...');
    
    // Step 1: Register business owner using unified endpoint
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const registerResult = await registerResponse.json();
    console.log('Registration Response:', registerResult);

    if (!registerResult.success) {
      console.error('Registration failed:', registerResult.message);
      return;
    }

    console.log('✅ Registration successful! OTP sent.');
    console.log('Note: Check server logs for OTP or implement email service');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test
testUnifiedRegistration();
