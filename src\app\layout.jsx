import "@/css/satoshi.css";
import "@/css/style.css";
import "react-toastify/dist/ReactToastify.css";

import { SidebarWrapper } from "@/components/Layouts/sidebar/SidebarWrapper";
import { ConditionalLayout } from "@/components/Layouts/ConditionalLayout";

import "flatpickr/dist/flatpickr.min.css";
import "jsvectormap/dist/jsvectormap.css";

import { Header } from "@/components/Layouts/header";
import NextTopLoader from "nextjs-toploader";
import { Providers } from "./providers";

export const metadata = {
  title: {
    template: "%s | BookMyService - Service Booking Platform",
    default: "BookMyService - Service Booking Platform",
  },
  description:
    "BookMyService is a comprehensive service booking platform that connects service providers with customers. Book services, manage bookings, and grow your business.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <Providers>
          <NextTopLoader color="#5750F1" showSpinner={false} />
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
        </Providers>
      </body>
    </html>
  );
}
