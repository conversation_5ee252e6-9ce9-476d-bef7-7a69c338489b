import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Employee from '@/models/Employee';
import EmployeeServiceAssignment from '@/models/EmployeeServiceAssignment';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import bcrypt from 'bcryptjs';

// GET - List all employees for a business owner
export async function GET(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded) {
      return apiResponse.error('Invalid token', 401);
    }
    
    // Handle both token structures (user.role and businessOwner.role)
    let businessOwnerId;
    let userRole;
    
    if (decoded.user) {
      businessOwnerId = decoded.user.id;
      userRole = decoded.user.role;
    } else if (decoded.businessOwner) {
      businessOwnerId = decoded.businessOwner.id;
      userRole = decoded.businessOwner.role;
    } else {
      return apiResponse.error('Invalid token structure', 401);
    }
    
    if (userRole !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'active';
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const skip = (page - 1) * limit;
    
    // Build query
    const query = { businessOwner: businessOwnerId };
    
    if (status !== 'all') {
      query.status = status;
    }
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { employeeId: { $regex: search, $options: 'i' } },
        { position: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Get employees with pagination
    const employees = await Employee.find(query)
      .select('-password')
      .populate('assignedServices.service', 'title name category')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const totalEmployees = await Employee.countDocuments(query);
    const totalPages = Math.ceil(totalEmployees / limit);
    
    // Get summary statistics
    const stats = await Employee.aggregate([
      { $match: { businessOwner: businessOwnerId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    const statusCounts = {
      active: 0,
      inactive: 0,
      suspended: 0,
      terminated: 0
    };
    
    stats.forEach(stat => {
      statusCounts[stat._id] = stat.count;
    });
    
    return apiResponse.success({
      employees,
      pagination: {
        currentPage: page,
        totalPages,
        totalEmployees,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      stats: statusCounts
    }, 'Employees fetched successfully');
    
  } catch (error) {
    console.error('Error fetching employees:', error);
    return apiResponse.error('Failed to fetch employees', 500);
  }
}

// POST - Create new employee
export async function POST(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded) {
      return apiResponse.error('Invalid token', 401);
    }
    
    // Handle both token structures
    let businessOwnerId;
    let userRole;
    
    if (decoded.user) {
      businessOwnerId = decoded.user.id;
      userRole = decoded.user.role;
    } else if (decoded.businessOwner) {
      businessOwnerId = decoded.businessOwner.id;
      userRole = decoded.businessOwner.role;
    } else {
      return apiResponse.error('Invalid token structure', 401);
    }
    
    if (userRole !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const employeeData = await request.json();
    
    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'phoneNumber', 'position', 'password'];
    for (const field of requiredFields) {
      if (!employeeData[field]) {
        return apiResponse.error(`${field} is required`, 400);
      }
    }
    
    // Check if email already exists
    const existingEmployee = await Employee.findOne({ email: employeeData.email });
    if (existingEmployee) {
      return apiResponse.error('Employee with this email already exists', 400);
    }
    
    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(employeeData.password, saltRounds);
    
    // Create employee
    const employee = new Employee({
      ...employeeData,
      password: hashedPassword,
      businessOwner: businessOwnerId,
      createdBy: businessOwnerId
    });
    
    await employee.save();
    
    // Remove password from response
    const employeeResponse = employee.toObject();
    delete employeeResponse.password;
    
    return apiResponse.success(employeeResponse, 'Employee created successfully', 201);
    
  } catch (error) {
    console.error('Error creating employee:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    if (error.code === 11000) {
      return apiResponse.error('Employee with this email already exists', 400);
    }
    
    return apiResponse.error('Failed to create employee', 500);
  }
}
