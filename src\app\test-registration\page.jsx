"use client";

import { useState } from 'react';

export default function TestRegistration() {
  const [formData, setFormData] = useState({
    role: '',
    firstName: '',
    lastName: '',
    email: '',
    password: 'test123',
    phoneNumber: '1234567890',
    businessName: '',
    businessCategory: '',
    businessDescription: '',
    businessAddress: '',
    city: '',
    state: '',
    zipCode: '',
    country: ''
  });

  const [debugInfo, setDebugInfo] = useState('Role: Not selected');
  const [testResult, setTestResult] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = { ...formData, [name]: value };
    
    console.log('=== FORM CHANGE DEBUG ===');
    console.log('Field changed:', name, '=', value);
    console.log('Previous role:', formData.role);
    console.log('New role:', newFormData.role);
    console.log('Full form data:', newFormData);
    console.log('========================');
    
    setFormData(newFormData);
    setDebugInfo(`Role: ${newFormData.role || 'Not selected'}`);
  };

  const testRegistration = async () => {
    if (!formData.role) {
      setTestResult('❌ Please select a role first!');
      return;
    }

    if (!formData.firstName || !formData.lastName || !formData.email) {
      setTestResult('❌ Please fill in required fields!');
      return;
    }

    const endpoint = formData.role === 'business_owner' 
      ? '/api/auth/registerBusinessOwner'
      : '/api/auth/register';

    const payload = formData.role === 'business_owner'
      ? {
          email: formData.email,
          password: formData.password,
          phoneNumber: formData.phoneNumber,
          ownerFirstName: formData.firstName,
          ownerLastName: formData.lastName,
          businessName: formData.businessName,
          businessCategory: formData.businessCategory,
          businessDescription: formData.businessDescription,
          businessAddress: formData.businessAddress,
          city: formData.city,
          state: formData.state,
          zipCode: formData.zipCode,
          country: formData.country,
          role: 'business_owner'
        }
      : {
          email: formData.email,
          password: formData.password,
          phoneNumber: formData.phoneNumber,
          firstName: formData.firstName,
          lastName: formData.lastName,
          role: 'user'
        };

    console.log('Testing registration with payload:', payload);
    console.log('Using endpoint:', endpoint);

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      console.log('Registration result:', result);
      
      if (result.success) {
        setTestResult(`✅ Registration successful: ${result.message}`);
      } else {
        setTestResult(`❌ Registration failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Registration error:', error);
      setTestResult(`❌ Registration error: ${error.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6 text-center">Test Registration Form</h1>
        
        {/* Debug Info */}
        <div className="bg-blue-50 p-4 rounded-md mb-6">
          <h3 className="font-semibold text-blue-800">Debug Info:</h3>
          <p className="text-blue-700">{debugInfo}</p>
        </div>

        <form className="space-y-4">
          {/* Role Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Role *
            </label>
            <select
              name="role"
              value={formData.role}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="" disabled>Select a role</option>
              <option value="user">User</option>
              <option value="business_owner">Business Owner</option>
            </select>
          </div>

          {/* Basic Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Business Owner Fields */}
          {formData.role === 'business_owner' && (
            <div className="bg-gray-50 p-4 rounded-md space-y-4">
              <h3 className="font-semibold text-gray-800">Business Owner Fields</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Business Name
                </label>
                <input
                  type="text"
                  name="businessName"
                  value={formData.businessName}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Business Category
                </label>
                <select
                  name="businessCategory"
                  value={formData.businessCategory}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Category</option>
                  <option value="Cleaning">Cleaning</option>
                  <option value="Plumbing">Plumbing</option>
                  <option value="Electrical">Electrical</option>
                  <option value="Gardening">Gardening</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Business Description
                </label>
                <input
                  type="text"
                  name="businessDescription"
                  value={formData.businessDescription}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}

          <button
            type="button"
            onClick={testRegistration}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Test Registration
          </button>
        </form>

        {/* Test Result */}
        {testResult && (
          <div className="mt-6 p-4 rounded-md bg-gray-50">
            <h3 className="font-semibold text-gray-800 mb-2">Test Result:</h3>
            <p className="text-gray-700">{testResult}</p>
          </div>
        )}

        {/* Form Data Display */}
        <div className="mt-6 p-4 rounded-md bg-gray-50">
          <h3 className="font-semibold text-gray-800 mb-2">Current Form Data:</h3>
          <pre className="text-sm text-gray-600 overflow-auto">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
