<!DOCTYPE html>
<html>
<head>
    <title>Test Business Owner Login</title>
</head>
<body>
    <h1>Test Business Owner Login</h1>
    
    <div>
        <h2>Create Test Users</h2>
        <button onclick="createTestUsers()">Create Test Users</button>
        <div id="createResult"></div>
    </div>
    
    <div>
        <h2>Test Business Owner Login</h2>
        <input type="email" id="email" value="<EMAIL>" placeholder="Email">
        <input type="password" id="password" value="password" placeholder="Password">
        <button onclick="testBusinessLogin()">Test Business Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div>
        <h2>Test Regular User Login</h2>
        <input type="email" id="userEmail" value="<EMAIL>" placeholder="Email">
        <input type="password" id="userPassword" value="password" placeholder="Password">
        <button onclick="testUserLogin()">Test User Login</button>
        <div id="userLoginResult"></div>
    </div>

    <script>
        async function createTestUsers() {
            try {
                const response = await fetch('/api/test-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const result = await response.json();
                document.getElementById('createResult').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                console.log('Create users result:', result);
            } catch (error) {
                document.getElementById('createResult').innerHTML = 
                    '<div style="color: red;">Error: ' + error.message + '</div>';
                console.error('Create users error:', error);
            }
        }
        
        async function testBusinessLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                console.log('Testing business owner login with:', { email, password: '***' });
                
                const response = await fetch('/api/auth/businessOwnerLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const result = await response.json();
                document.getElementById('loginResult').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                console.log('Business login result:', result);
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    '<div style="color: red;">Error: ' + error.message + '</div>';
                console.error('Business login error:', error);
            }
        }
        
        async function testUserLogin() {
            const email = document.getElementById('userEmail').value;
            const password = document.getElementById('userPassword').value;
            
            try {
                console.log('Testing user login with:', { email, password: '***' });
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const result = await response.json();
                document.getElementById('userLoginResult').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                console.log('User login result:', result);
            } catch (error) {
                document.getElementById('userLoginResult').innerHTML = 
                    '<div style="color: red;">Error: ' + error.message + '</div>';
                console.error('User login error:', error);
            }
        }
    </script>
</body>
</html>
