// Test script for registration endpoints
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testUserRegistration() {
  console.log('\n=== Testing User Registration ===');
  
  const userData = {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'password123',
    phoneNumber: '1234567890',
    role: 'user'
  };

  try {
    const response = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    });

    const data = await response.json();
    console.log('User Registration Response:', data);
    return data.success;
  } catch (error) {
    console.error('User Registration Error:', error);
    return false;
  }
}

async function testBusinessOwnerRegistration() {
  console.log('\n=== Testing Business Owner Registration ===');
  
  const businessOwnerData = {
    ownerFirstName: 'Test',
    ownerLastName: 'Owner',
    email: '<EMAIL>',
    password: 'password123',
    phoneNumber: '1234567891',
    businessName: 'Test Business',
    businessCategory: 'Cleaning',
    businessDescription: 'Test business description',
    businessAddress: '123 Test St',
    city: 'Test City',
    state: 'Test State',
    zipCode: '12345',
    country: 'Test Country'
  };

  try {
    const response = await fetch(`${BASE_URL}/api/auth/registerBusinessOwner`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(businessOwnerData)
    });

    const data = await response.json();
    console.log('Business Owner Registration Response:', data);
    return data.success;
  } catch (error) {
    console.error('Business Owner Registration Error:', error);
    return false;
  }
}

async function runTests() {
  console.log('Starting Registration Tests...');
  
  const userTest = await testUserRegistration();
  const businessOwnerTest = await testBusinessOwnerRegistration();
  
  console.log('\n=== Test Results ===');
  console.log('User Registration:', userTest ? 'PASS' : 'FAIL');
  console.log('Business Owner Registration:', businessOwnerTest ? 'PASS' : 'FAIL');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testUserRegistration, testBusinessOwnerRegistration };
