// "use client";

// import { useState, useEffect } from 'react';
// import { useAuth } from '@/contexts/AuthContext';
// import { toast } from 'react-toastify';
// import ServiceModal from '@/components/Services/ServiceModal';
// import { serviceAlerts } from '@/utils/alerts';

// function PlusIcon(props) {
//   return (
//     <svg
//       width={20}
//       height={20}
//       viewBox="0 0 24 24"
//       fill="none"
//       stroke="currentColor"
//       strokeWidth={2}
//       strokeLinecap="round"
//       strokeLinejoin="round"
//       {...props}
//     >
//       <line x1="12" y1="5" x2="12" y2="19"/>
//       <line x1="5" y1="12" x2="19" y2="12"/>
//     </svg>
//   );
// }

// function EditIcon(props) {
//   return (
//     <svg
//       width={16}
//       height={16}
//       viewBox="0 0 24 24"
//       fill="none"
//       stroke="currentColor"
//       strokeWidth={2}
//       strokeLinecap="round"
//       strokeLinejoin="round"
//       {...props}
//     >
//       <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
//       <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
//     </svg>
//   );
// }

// function TrashIcon(props) {
//   return (
//     <svg
//       width={16}
//       height={16}
//       viewBox="0 0 24 24"
//       fill="none"
//       stroke="currentColor"
//       strokeWidth={2}
//       strokeLinecap="round"
//       strokeLinejoin="round"
//       {...props}
//     >
//       <polyline points="3,6 5,6 21,6"/>
//       <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
//       <line x1="10" y1="11" x2="10" y2="17"/>
//       <line x1="14" y1="11" x2="14" y2="17"/>
//     </svg>
//   );
// }

// function EyeIcon(props) {
//   return (
//     <svg
//       width={16}
//       height={16}
//       viewBox="0 0 24 24"
//       fill="none"
//       stroke="currentColor"
//       strokeWidth={2}
//       strokeLinecap="round"
//       strokeLinejoin="round"
//       {...props}
//     >
//       <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
//       <circle cx="12" cy="12" r="3"/>
//     </svg>
//   );
// }

// const statusColors = {
//   active: 'bg-green-100 text-green-800',
//   inactive: 'bg-red-100 text-red-800',
//   pending: 'bg-yellow-100 text-yellow-800'
// };

// export default function ManageServicesPage() {
//   const { user } = useAuth();
//   const [services, setServices] = useState([]);
//   const [loading, setLoading] = useState(true);
//   const [filter, setFilter] = useState('all');

//   // Modal states
//   const [modalOpen, setModalOpen] = useState(false);
//   const [editingService, setEditingService] = useState(null);
//   const [modalLoading, setModalLoading] = useState(false);

//   useEffect(() => {
//     if (user) {
//       fetchServices();
//     }
//   }, [user]);

//   const fetchServices = async () => {
//     try {
//       const response = await fetch('/api/business/services', {
//         headers: {
//           'Authorization': `Bearer ${localStorage.getItem('token')}`
//         }
//       });
      
//       const data = await response.json();
      
//       if (data.success) {
//         setServices(data.data);
//       } else {
//         toast.error('Failed to fetch services');
//       }
//     } catch (error) {
//       console.error('Error fetching services:', error);
//       toast.error('Error loading services');
//     } finally {
//       setLoading(false);
//     }
//   };

//   // Modal functions
//   const handleAddService = () => {
//     setEditingService(null);
//     setModalOpen(true);
//   };

//   const handleEditService = (service) => {
//     setEditingService(service);
//     setModalOpen(true);
//   };

//   const handleCloseModal = () => {
//     setModalOpen(false);
//     setEditingService(null);
//     setModalLoading(false);
//   };

//   const handleSaveService = async (serviceData) => {
//     setModalLoading(true);

//     try {
//       const url = editingService
//         ? `/api/business/services/${editingService._id}`
//         : '/api/business/services';

//       const method = editingService ? 'PUT' : 'POST';

//       const response = await fetch(url, {
//         method,
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': `Bearer ${localStorage.getItem('token')}`
//         },
//         body: JSON.stringify(serviceData)
//       });

//       const data = await response.json();

//       if (data.success) {
//         if (editingService) {
//           serviceAlerts.updated(serviceData.title);
//         } else {
//           serviceAlerts.created(serviceData.title);
//         }
//         fetchServices(); // Refresh the list
//         handleCloseModal();
//       } else {
//         if (editingService) {
//           serviceAlerts.updateError(data.message);
//         } else {
//           serviceAlerts.createError(data.message);
//         }
//       }
//     } catch (error) {
//       console.error('Error saving service:', error);
//       if (editingService) {
//         serviceAlerts.updateError('Network error occurred');
//       } else {
//         serviceAlerts.createError('Network error occurred');
//       }
//     } finally {
//       setModalLoading(false);
//     }
//   };

//   const handleToggleStatus = async (service) => {
//     try {
//       const result = await serviceAlerts.confirmStatusToggle(service.title, service.status);

//       if (result.isConfirmed) {
//         const response = await fetch(`/api/business/services/${service._id}`, {
//           method: 'PATCH',
//           headers: {
//             'Content-Type': 'application/json',
//             'Authorization': `Bearer ${localStorage.getItem('token')}`
//           },
//           body: JSON.stringify({ action: 'toggle-status' })
//         });

//         const data = await response.json();

//         if (data.success) {
//           const newStatus = service.status === 'active' ? 'inactive' : 'active';
//           serviceAlerts.statusToggled(service.title, newStatus);
//           fetchServices(); // Refresh the list
//         } else {
//           serviceAlerts.updateError(data.message);
//         }
//       }
//     } catch (error) {
//       console.error('Error updating service status:', error);
//       serviceAlerts.updateError('Network error occurred');
//     }
//   };

//   const handleDeleteService = async (service) => {
//     try {
//       const result = await serviceAlerts.confirmDelete(service.title);

//       if (result.isConfirmed) {
//         const response = await fetch(`/api/business/services/${service._id}`, {
//           method: 'DELETE',
//           headers: {
//             'Authorization': `Bearer ${localStorage.getItem('token')}`
//           }
//         });

//         const data = await response.json();

//         if (data.success) {
//           serviceAlerts.deleted(service.title);
//           fetchServices(); // Refresh the list
//         } else {
//           serviceAlerts.deleteError(data.message);
//         }
//       }
//     } catch (error) {
//       console.error('Error deleting service:', error);
//       serviceAlerts.deleteError('Network error occurred');
//     }
//   };

//   console.log("services", services);
  
//   const filteredServices = services.services.filter(service => {
//     if (filter === 'all') return true;
//     return service.status === filter;
//   });

//   if (!user) {
//     return (
//       <div className="p-6 text-center">
//         <p className="text-gray-500">Please login to manage your services.</p>
//       </div>
//     );
//   }

//   if (loading) {
//     return (
//       <div className="p-6">
//         <div className="animate-pulse">
//           <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
//           <div className="space-y-4">
//             {[1, 2, 3].map((i) => (
//               <div key={i} className="bg-white rounded-lg shadow p-6">
//                 <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
//                 <div className="h-4 bg-gray-200 rounded w-1/2"></div>
//               </div>
//             ))}
//           </div>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="p-6">
//       <div className="flex items-center justify-between mb-8">
//         <div>
//           <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
//             Manage Services
//           </h1>
//           <p className="text-gray-600 dark:text-gray-400">
//             Create, edit, and manage your service offerings
//           </p>
//         </div>
//         <button
//           onClick={handleAddService}
//           className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
//         >
//           <PlusIcon />
//           <span>Add Service</span>
//         </button>
//       </div>

//       {/* Filter Tabs */}
//       <div className="mb-6">
//         <div className="border-b border-gray-200 dark:border-gray-700">
//           <nav className="-mb-px flex space-x-8">
//             {[
//               { key: 'all', label: 'All Services' },
//               { key: 'active', label: 'Active' },
//               { key: 'inactive', label: 'Inactive' },
//               { key: 'pending', label: 'Pending' }
//             ].map((tab) => (
//               <button
//                 key={tab.key}
//                 onClick={() => setFilter(tab.key)}
//                 className={`py-2 px-1 border-b-2 font-medium text-sm ${
//                   filter === tab.key
//                     ? 'border-blue-500 text-blue-600'
//                     : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
//                 }`}
//               >
//                 {tab.label}
//                 {tab.key !== 'all' && (
//                   <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
//                     {services.filter(s => s.status === tab.key).length}
//                   </span>
//                 )}
//               </button>
//             ))}
//           </nav>
//         </div>
//       </div>

//       {/* Services Grid */}
//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//         {filteredServices.map((service) => (
//           <div key={service._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
//             {service.images && service.images.length > 0 && (
//               <img
//                 src={service.images[0]}
//                 alt={service.title}
//                 className="w-full h-48 object-cover"
//               />
//             )}
            
//             <div className="p-6">
//               <div className="flex items-start justify-between mb-2">
//                 <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
//                   {service.title}
//                 </h3>
//                 <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[service.status]}`}>
//                   {service.status.toUpperCase()}
//                 </span>
//               </div>
              
//               <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
//                 {service.description}
//               </p>
              
//               <div className="flex items-center justify-between mb-4">
//                 <span className="text-lg font-bold text-blue-600">
//                   ${service.price}
//                 </span>
//                 <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
//                   {service.category}
//                 </span>
//               </div>
              
//               <div className="flex items-center justify-between">
//                 <div className="flex space-x-2">
//                   <button
//                     onClick={() => handleEditService(service)}
//                     className="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors"
//                     title="Edit Service"
//                   >
//                     <EditIcon />
//                   </button>

//                   <button
//                     onClick={() => handleToggleStatus(service)}
//                     className={`p-2 rounded-lg transition-colors ${
//                       service.status === 'active'
//                         ? 'bg-red-100 hover:bg-red-200 text-red-700'
//                         : 'bg-green-100 hover:bg-green-200 text-green-700'
//                     }`}
//                     title={service.status === 'active' ? 'Deactivate' : 'Activate'}
//                   >
//                     <EyeIcon />
//                   </button>

//                   <button
//                     onClick={() => handleDeleteService(service)}
//                     className="bg-red-100 hover:bg-red-200 text-red-700 p-2 rounded-lg transition-colors"
//                     title="Delete Service"
//                   >
//                     <TrashIcon />
//                   </button>
//                 </div>
                
//                 <div className="text-sm text-gray-500">
//                   {service.bookingsCount || 0} bookings
//                 </div>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       {filteredServices.length === 0 && (
//         <div className="text-center py-12">
//           <PlusIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
//           <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
//             No services found
//           </h3>
//           <p className="text-gray-500 dark:text-gray-400 mb-4">
//             {filter === 'all' 
//               ? "You haven't created any services yet. Get started by adding your first service!"
//               : `No ${filter} services found.`
//             }
//           </p>
//           {filter === 'all' && (
//             <button
//               onClick={handleAddService}
//               className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center space-x-2 transition-colors"
//             >
//               <PlusIcon />
//               <span>Add Your First Service</span>
//             </button>
//           )}
//         </div>
//       )}

//       {/* Service Modal */}
//       <ServiceModal
//         open={modalOpen}
//         onClose={handleCloseModal}
//         onSave={handleSaveService}
//         service={editingService}
//         loading={modalLoading}
//       />
//     </div>
//   );
// }
```jsx
"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import ServiceModal from '@/components/Services/ServiceModal';
import { serviceAlerts } from '@/utils/alerts';

function PlusIcon(props) {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <line x1="12" y1="5" x2="12" y2="19"/>
      <line x1="5" y1="12" x2="19" y2="12"/>
    </svg>
  );
}

function EditIcon(props) {
  return (
    <svg
      width={16}
      height={16}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
    </svg>
  );
}

function TrashIcon(props) {
  return (
    <svg
      width={16}
      height={16}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <polyline points="3,6 5,6 21,6"/>
      <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
      <line x1="10" y1="11" x2="10" y2="17"/>
      <line x1="14" y1="11" x2="14" y2="17"/>
    </svg>
  );
}

function EyeIcon(props) {
  return (
    <svg
      width={16}
      height={16}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  );
}

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-red-100 text-red-800',
  pending: 'bg-yellow-100 text-yellow-800'
};

export default function ManageServicesPage() {
  const { user } = useAuth();
  const [services, setServices] = useState({ services: [], pagination: {} });
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  // Modal states
  const [modalOpen, setModalOpen] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [modalLoading, setModalLoading] = useState(false);

  useEffect(() => {
    if (user) {
      fetchServices();
    }
  }, [user]);

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/business/services', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const data = await response.json();
      
      if (data.success) {
        setServices({
          services: data.data?.services || [],
          pagination: data.data?.pagination || { page: 1, limit: 10, total: 0, pages: 0 }
        });
      } else {
        toast.error('Failed to fetch services');
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      toast.error('Error loading services');
    } finally {
      setLoading(false);
    }
  };

  // Modal functions
  const handleAddService = () => {
    setEditingService(null);
    setModalOpen(true);
  };

  const handleEditService = (service) => {
    setEditingService(service);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setEditingService(null);
    setModalLoading(false);
  };

  const handleSaveService = async (serviceData) => {
    setModalLoading(true);

    try {
      const url = editingService
        ? `/api/business/services/${editingService._id}`
        : '/api/business/services';

      const method = editingService ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(serviceData)
      });

      const data = await response.json();

      if (data.success) {
        if (editingService) {
          serviceAlerts.updated(serviceData.title);
        } else {
          serviceAlerts.created(serviceData.title);
        }
        fetchServices(); // Refresh the list
        handleCloseModal();
      } else {
        if (editingService) {
          serviceAlerts.updateError(data.message);
        } else {
          serviceAlerts.createError(data.message);
        }
      }
    } catch (error) {
      console.error('Error saving service:', error);
      if (editingService) {
        serviceAlerts.updateError('Network error occurred');
      } else {
        serviceAlerts.createError('Network error occurred');
      }
    } finally {
      setModalLoading(false);
    }
  };

  const handleToggleStatus = async (service) => {
    try {
      const result = await serviceAlerts.confirmStatusToggle(service.title, service.status);

      if (result.isConfirmed) {
        const response = await fetch(`/api/business/services/${service._id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({ action: 'toggle-status' })
        });

        const data = await response.json();

        if (data.success) {
          const newStatus = service.status === 'active' ? 'inactive' : 'active';
          serviceAlerts.statusToggled(service.title, newStatus);
          fetchServices(); // Refresh the list
        } else {
          serviceAlerts.updateError(data.message);
        }
      }
    } catch (error) {
      console.error('Error updating service status:', error);
      serviceAlerts.updateError('Network error occurred');
    }
  };

  const handleDeleteService = async (service) => {
    try {
      const result = await serviceAlerts.confirmDelete(service.title);

      if (result.isConfirmed) {
        const response = await fetch(`/api/business/services/${service._id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        const data = await response.json();

        if (data.success) {
          serviceAlerts.deleted(service.title);
          fetchServices(); // Refresh the list
        } else {
          serviceAlerts.deleteError(data.message);
        }
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      serviceAlerts.deleteError('Network error occurred');
    }
  };

  console.log("services", services);
  
  const filteredServices = services.services.filter(service => {
    if (filter === 'all') return true;
    return service.status === filter;
  });

  if (!user) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">Please login to manage your services.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Manage Services
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Create, edit, and manage your service offerings
          </p>
        </div>
        <button
          onClick={handleAddService}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <PlusIcon />
          <span>Add Service</span>
        </button>
      </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'all', label: 'All Services' },
              { key: 'active', label: 'Active' },
              { key: 'inactive', label: 'Inactive' },
              { key: 'pending', label: 'Pending' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                {tab.key !== 'all' && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                    {services.services.filter(s => s.status === tab.key).length}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredServices.map((service) => (
          <div key={service._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            {service.images && service.images.length > 0 && (
              <img
                src={service.images[0]}
                alt={service.title}
                className="w-full h-48 object-cover"
              />
            )}
            
            <div className="p-6">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {service.title}
                </h3>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[service.status]}`}>
                  {service.status.toUpperCase()}
                </span>
              </div>
              
              <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                {service.description}
              </p>
              <div className="flex items-center justify-between mb-4">
                <span className="text-lg font-bold text-blue-600">
                  ${service.price}
                </span>
                <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {service.category}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEditService(service)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors"
                    title="Edit Service"
                  >
                    <EditIcon />
                  </button>

                  <button
                    onClick={() => handleToggleStatus(service)}
                    className={`p-2 rounded-lg transition-colors ${
                      service.status === 'active'
                        ? 'bg-red-100 hover:bg-red-200 text-red-700'
                        : 'bg-green-100 hover:bg-green-200 text-green-700'
                    }`}
                    title={service.status === 'active' ? 'Deactivate' : 'Activate'}
                  >
                    <EyeIcon />
                  </button>

                  <button
                    onClick={() => handleDeleteService(service)}
                    className="bg-red-100 hover:bg-red-200 text-red-700 p-2 rounded-lg transition-colors"
                    title="Delete Service"
                  >
                    <TrashIcon />
                  </button>
                </div>
                
                <div className="text-sm text-gray-500">
                  {service.bookingsCount || 0} bookings
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredServices.length === 0 && (
        <div className="text-center py-12">
          <PlusIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No services found
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {filter === 'all' 
              ? "You haven't created any services yet. Get started by adding your first service!"
              : `No ${filter} services found.`
            }
          </p>
          {filter === 'all' && (
            <button
              onClick={handleAddService}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center space-x-2 transition-colors"
            >
              <PlusIcon />
              <span>Add Your First Service</span>
            </button>
          )}
        </div>
      )}

      {/* Service Modal */}
      <ServiceModal
        open={modalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveService}
        service={editingService}
        loading={modalLoading}
      />
    </div>
  );
}