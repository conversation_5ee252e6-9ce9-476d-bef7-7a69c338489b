// Complete test for registration and OTP verification
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

// Extract OTP from server logs (for testing purposes)
function extractOTPFromLogs(email) {
  // In a real scenario, you'd get this from email
  // For testing, we'll use a mock OTP
  return '123456'; // Mock OTP for testing
}

async function testCompleteBusinessOwnerFlow() {
  console.log('\n=== Testing Complete Business Owner Flow ===');
  
  const businessOwnerData = {
    ownerFirstName: 'John',
    ownerLastName: 'Doe',
    email: '<EMAIL>',
    password: 'password123',
    phoneNumber: '9876543210',
    businessName: 'John\'s Cleaning Service',
    businessCategory: 'Cleaning',
    businessDescription: 'Professional cleaning services',
    businessAddress: '456 Business Ave',
    city: 'Business City',
    state: 'Business State',
    zipCode: '54321',
    country: 'USA'
  };

  try {
    // Step 1: Register Business Owner
    console.log('Step 1: Registering business owner...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/registerBusinessOwner`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(businessOwnerData)
    });

    const registerData = await registerResponse.json();
    console.log('Registration Response:', registerData);

    if (!registerData.success) {
      throw new Error('Registration failed: ' + registerData.message);
    }

    // Step 2: Simulate OTP verification (using mock OTP)
    console.log('Step 2: Verifying OTP...');
    const mockOTP = '123456'; // In real scenario, extract from email
    
    const verifyResponse = await fetch(`${BASE_URL}/api/auth/verifyAndCreateBusinessOwner`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include', // Important for cookies
      body: JSON.stringify({ otp: mockOTP })
    });

    const verifyData = await verifyResponse.json();
    console.log('Verification Response:', verifyData);

    return verifyData.success;

  } catch (error) {
    console.error('Complete Flow Error:', error);
    return false;
  }
}

async function testCompleteUserFlow() {
  console.log('\n=== Testing Complete User Flow ===');
  
  const userData = {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    password: 'password123',
    phoneNumber: '5555555555',
    role: 'user'
  };

  try {
    // Step 1: Register User
    console.log('Step 1: Registering user...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    });

    const registerData = await registerResponse.json();
    console.log('Registration Response:', registerData);

    if (!registerData.success) {
      throw new Error('Registration failed: ' + registerData.message);
    }

    // Step 2: Simulate OTP verification
    console.log('Step 2: Verifying OTP...');
    const mockOTP = '123456';
    
    const verifyResponse = await fetch(`${BASE_URL}/api/auth/verifyAndCreateUser`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ otp: mockOTP })
    });

    const verifyData = await verifyResponse.json();
    console.log('Verification Response:', verifyData);

    return verifyData.success;

  } catch (error) {
    console.error('Complete Flow Error:', error);
    return false;
  }
}

async function runCompleteTests() {
  console.log('Starting Complete Registration Flow Tests...');
  
  const userFlowTest = await testCompleteUserFlow();
  const businessOwnerFlowTest = await testCompleteBusinessOwnerFlow();
  
  console.log('\n=== Complete Flow Test Results ===');
  console.log('User Complete Flow:', userFlowTest ? 'PASS' : 'FAIL');
  console.log('Business Owner Complete Flow:', businessOwnerFlowTest ? 'PASS' : 'FAIL');
}

if (require.main === module) {
  runCompleteTests().catch(console.error);
}

module.exports = { testCompleteBusinessOwnerFlow, testCompleteUserFlow };
