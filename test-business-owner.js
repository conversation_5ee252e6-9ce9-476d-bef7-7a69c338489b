// Test script for business owner registration
const testBusinessOwnerRegistration = async () => {
  const testData = {
    ownerFirstName: "John",
    ownerLastName: "Business",
    email: "<EMAIL>",
    password: "password123",
    phoneNumber: "1234567890",
    businessName: "Test Business Services",
    businessCategory: "Cleaning",
    businessDescription: "Professional cleaning services for homes and offices",
    businessAddress: "123 Business Street",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    country: "USA"
  };

  try {
    console.log('Testing Business Owner Registration...');
    
    // Step 1: Register business owner
    const registerResponse = await fetch('http://localhost:3000/api/auth/registerBusinessOwner', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const registerResult = await registerResponse.json();
    console.log('Registration Response:', registerResult);

    if (!registerResult.success) {
      console.error('Registration failed:', registerResult.message);
      return;
    }

    console.log('✅ Registration successful! OTP sent.');
    
    // For testing, we'll simulate OTP verification
    // In real scenario, user would enter the OTP from email
    console.log('Note: In real scenario, user would enter OTP from email');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test
testBusinessOwnerRegistration();
