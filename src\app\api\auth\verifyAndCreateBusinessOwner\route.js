import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { hashPassword, signToken } from '@/lib/auth-utils';
import { apiResponse } from '@/lib/apiResponse';

export async function POST(request) {
  try {
    await connectDB();

    const { otp } = await request.json();
    const cookieStore = await cookies();

    // Get stored OTP and business owner data from cookies
    const storedOtp = cookieStore.get('otp')?.value;
    const businessOwnerData = cookieStore.get('business_owner_data')?.value;

    console.log('OTP Verification - Stored OTP:', storedOtp);
    console.log('OTP Verification - Received OTP:', otp);
    console.log('OTP Verification - Business Owner Data exists:', !!businessOwnerData);

    if (!storedOtp || !businessOwnerData) {
      return apiResponse.error('Session expired. Please register again.', 400);
    }

    // Verify OTP
    if (otp !== storedOtp) {
      return apiResponse.error('Invalid OTP', 400);
    }

    // Parse business owner data
    const parsedData = JSON.parse(businessOwnerData);

    console.log('Parsed Business Owner Data:', {
      personalInfo: parsedData.personalInfo,
      businessInfo: parsedData.businessInfo
    });

    // Hash password
    const hashedPassword = await hashPassword(parsedData.personalInfo.password);

    // Create business owner as a User with business_owner role
    const businessOwnerUser = await User.create({
      firstName: parsedData.personalInfo.ownerFirstName,
      lastName: parsedData.personalInfo.ownerLastName,
      email: parsedData.personalInfo.email,
      password: hashedPassword,
      phoneNumber: parsedData.personalInfo.phoneNumber,
      businessName: parsedData.businessInfo.businessName,
      businessDescription: parsedData.businessInfo.businessDescription,
      businessAddress: parsedData.businessInfo.businessAddress,
      businessPhone: parsedData.personalInfo.phoneNumber,
      businessCategory: parsedData.businessInfo.businessCategory,
      city: parsedData.businessInfo.city,
      state: parsedData.businessInfo.state,
      zipCode: parsedData.businessInfo.zipCode,
      country: parsedData.businessInfo.country,
      isEmailVerified: true,
      role: 'business_owner',
      isActive: true,
    });

    console.log('Business Owner Created:', {
      id: businessOwnerUser._id,
      email: businessOwnerUser.email,
      businessName: businessOwnerUser.businessName,
      role: businessOwnerUser.role
    });

    // Generate JWT token
    const payload = {
      userId: businessOwnerUser._id,
      email: businessOwnerUser.email,
      role: businessOwnerUser.role,
    };
    const token = signToken(payload);

    // Clear cookies
    const response = NextResponse.json({
      success: true,
      message: 'Business owner registered successfully',
      data: {
        user: {
          id: businessOwnerUser._id,
          firstName: businessOwnerUser.firstName,
          lastName: businessOwnerUser.lastName,
          email: businessOwnerUser.email,
          businessName: businessOwnerUser.businessName,
          businessDescription: businessOwnerUser.businessDescription,
          businessAddress: businessOwnerUser.businessAddress,
          businessCategory: businessOwnerUser.businessCategory,
          city: businessOwnerUser.city,
          state: businessOwnerUser.state,
          zipCode: businessOwnerUser.zipCode,
          country: businessOwnerUser.country,
          role: businessOwnerUser.role,
        },
        token
      }
    });

    response.cookies.delete('business_owner_data');
    response.cookies.delete('otp');

    return response;

  } catch (error) {
    console.error('Business Owner Creation Error:', error);

    // Handle specific validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation failed: ${validationErrors.join(', ')}`, 400);
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
      return apiResponse.error('Email already exists', 400);
    }

    return apiResponse.error('Failed to create business owner account', 500);
  }
}