import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import connectDB from '@/lib/mongodb';
import BusinessOwner from '@/models/BusinessOwner';
import { hashPassword, signToken } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();

    const { otp } = await request.json();
    const cookieStore = await cookies();

    // Get stored OTP and business owner data from cookies
    const storedOtp = cookieStore.get('otp')?.value;
    const businessOwnerData = cookieStore.get('business_owner_data')?.value;

    console.log('OTP Verification - Stored OTP:', storedOtp);
    console.log('OTP Verification - Received OTP:', otp);
    console.log('OTP Verification - Business Owner Data exists:', !!businessOwnerData);

    if (!storedOtp || !businessOwnerData) {
      return NextResponse.json({
        success: false,
        message: 'Session expired. Please register again.'
      }, { status: 400 });
    }

    // Verify OTP
    if (otp !== storedOtp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP'
      }, { status: 400 });
    }

    // Parse business owner data
    const parsedData = JSON.parse(businessOwnerData);

    console.log('Parsed Business Owner Data:', {
      personalInfo: parsedData.personalInfo,
      businessInfo: parsedData.businessInfo
    });

    // Hash password
    const hashedPassword = await hashPassword(parsedData.personalInfo.password);

    // Create business owner in BusinessOwner collection
    const businessOwner = await BusinessOwner.create({
      ownerFirstName: parsedData.personalInfo.ownerFirstName,
      ownerLastName: parsedData.personalInfo.ownerLastName,
      email: parsedData.personalInfo.email,
      password: hashedPassword,
      phoneNumber: parsedData.personalInfo.phoneNumber,
      businessName: parsedData.businessInfo.businessName,
      businessCategory: parsedData.businessInfo.businessCategory,
      businessDescription: parsedData.businessInfo.businessDescription,
      businessAddress: parsedData.businessInfo.businessAddress,
      city: parsedData.businessInfo.city,
      state: parsedData.businessInfo.state,
      zipCode: parsedData.businessInfo.zipCode,
      country: parsedData.businessInfo.country,
      isEmailVerified: true,
      role: 'business_owner',
      isActive: true,
    });

    console.log('Business Owner Created:', {
      id: businessOwner._id,
      email: businessOwner.email,
      businessName: businessOwner.businessName,
      role: businessOwner.role
    });

    // Generate JWT token
    const payload = {
      user: {
        id: businessOwner._id,
        role: businessOwner.role,
        firstName: businessOwner.ownerFirstName,
        lastName: businessOwner.ownerLastName,
        email: businessOwner.email,
      },
    };
    const token = signToken(payload);

    // Clear cookies
    const response = NextResponse.json({
      success: true,
      message: 'Business owner registered successfully',
      data: {
        user: {
          id: businessOwner._id,
          firstName: businessOwner.ownerFirstName,
          lastName: businessOwner.ownerLastName,
          email: businessOwner.email,
          businessName: businessOwner.businessName,
          businessDescription: businessOwner.businessDescription,
          businessAddress: businessOwner.businessAddress,
          businessCategory: businessOwner.businessCategory,
          city: businessOwner.city,
          state: businessOwner.state,
          zipCode: businessOwner.zipCode,
          country: businessOwner.country,
          role: businessOwner.role,
        },
        token
      }
    });

    response.cookies.delete('business_owner_data');
    response.cookies.delete('otp');

    return response;

  } catch (error) {
    console.error('Business Owner Creation Error:', error);

    // Handle specific validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json({
        success: false,
        message: `Validation failed: ${validationErrors.join(', ')}`
      }, { status: 400 });
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Failed to create business owner account'
    }, { status: 500 });
  }
}