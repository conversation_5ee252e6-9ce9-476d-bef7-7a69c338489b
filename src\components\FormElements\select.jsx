"use client";

import { ChevronUpIcon } from "@/assets/icons.jsx";
import { cn } from "@/lib/utils";
import { useId, useState } from "react";

const Select = ({ label, items, value, onChange, name, required, placeholder }) => {
  const handleSelectChange = (e) => {
    // Ensure the onChange handler receives the event object with name and value
    if (onChange) {
      onChange(e);
    }
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-dark dark:text-white">{label}</label>
      <select
        name={name}
        value={value}
        onChange={handleSelectChange}
        required={required}
        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary dark:bg-gray-800 dark:border-gray-600 dark:text-white px-3 py-2"
      >
        <option value="" disabled>
          {placeholder || "Select an option"}
        </option>
        {items.map((item) => (
          <option key={item.value} value={item.value}>
            {item.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default Select;