"use client";

import { ChevronUpIcon } from "@/assets/icons.jsx";
import { cn } from "@/lib/utils";
import { useId, useState } from "react";
const Select = ({ label, items, value, onChange, name, required, placeholder }) => {
  return (
    <div className="mb-4">
      <label className="block text-sm font-medium">{label}</label>
      <select
        name={name}
        value={value}
        onChange={onChange}
        required={required}
        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
      >
        <option value="" disabled>
          {placeholder || "Select an option"}
        </option>
        {items.map((item) => (
          <option key={item.value} value={item.value}>
            {item.label}
          </option>
        ))}
      </select>
    </div>
  );
};
export default Select;