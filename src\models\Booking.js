import mongoose from 'mongoose';

const bookingSchema = new mongoose.Schema({
  // Customer who made the booking
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Service being booked
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: true
  },
  
  // Business owner providing the service
  businessOwner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Assigned employee (optional - for businesses with employees)
  assignedEmployee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: false
  },
  
  // Booking details
  preferredDate: {
    type: Date,
    required: true
  },
  
  preferredTime: {
    type: String, // Format: "HH:MM" (24-hour format)
    required: false
  },
  
  // Customer message/requirements
  message: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Booking status
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'rejected', 'in_progress', 'completed', 'cancelled'],
    default: 'pending'
  },
  
  // Financial details
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  
  // Payment details
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'refunded', 'failed'],
    default: 'pending'
  },
  
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'bank_transfer', 'paypal', 'stripe'],
    required: false
  },
  
  paymentTransactionId: {
    type: String,
    required: false
  },
  
  // Service delivery details
  actualStartTime: {
    type: Date,
    required: false
  },
  
  actualEndTime: {
    type: Date,
    required: false
  },
  
  // Customer address (if service is at customer location)
  customerAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // Rating and review (after service completion)
  rating: {
    type: Number,
    min: 1,
    max: 5,
    required: false
  },
  
  review: {
    type: String,
    trim: true,
    maxlength: 1000,
    required: false
  },
  
  // Business owner response to review
  businessOwnerResponse: {
    type: String,
    trim: true,
    maxlength: 500,
    required: false
  },
  
  // Timestamps for different status changes
  confirmedAt: {
    type: Date,
    required: false
  },
  
  rejectedAt: {
    type: Date,
    required: false
  },
  
  startedAt: {
    type: Date,
    required: false
  },
  
  completedAt: {
    type: Date,
    required: false
  },
  
  cancelledAt: {
    type: Date,
    required: false
  },
  
  cancelledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  
  // Cancellation reason
  cancellationReason: {
    type: String,
    trim: true,
    maxlength: 500,
    required: false
  },
  
  // Additional notes from business owner
  businessOwnerNotes: {
    type: String,
    trim: true,
    maxlength: 1000,
    required: false
  },
  
  // Special requirements or instructions
  specialRequirements: {
    type: String,
    trim: true,
    maxlength: 500,
    required: false
  },
  
  // Estimated duration (in minutes)
  estimatedDuration: {
    type: Number,
    min: 15,
    required: false
  },
  
  // Actual duration (in minutes)
  actualDuration: {
    type: Number,
    min: 0,
    required: false
  },
  
  // Communication history
  communications: [{
    from: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    message: {
      type: String,
      required: true,
      trim: true,
      maxlength: 1000
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['message', 'status_update', 'system'],
      default: 'message'
    }
  }],
  
  // Attachments (photos, documents, etc.)
  attachments: [{
    filename: String,
    url: String,
    type: {
      type: String,
      enum: ['image', 'document', 'other']
    },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Metadata
  isActive: {
    type: Boolean,
    default: true
  },
  
  // For soft delete
  isDeleted: {
    type: Boolean,
    default: false
  },
  
  // Track if service was deleted after booking
  serviceDeleted: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true, // Adds createdAt and updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
bookingSchema.index({ user: 1, createdAt: -1 });
bookingSchema.index({ businessOwner: 1, createdAt: -1 });
bookingSchema.index({ service: 1, createdAt: -1 });
bookingSchema.index({ status: 1, createdAt: -1 });
bookingSchema.index({ preferredDate: 1 });
bookingSchema.index({ paymentStatus: 1 });

// Virtual for booking duration in a readable format
bookingSchema.virtual('durationFormatted').get(function() {
  if (!this.actualDuration) return null;
  
  const hours = Math.floor(this.actualDuration / 60);
  const minutes = this.actualDuration % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
});

// Virtual for booking age
bookingSchema.virtual('ageInDays').get(function() {
  const now = new Date();
  const created = this.createdAt;
  const diffTime = Math.abs(now - created);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Pre-save middleware
bookingSchema.pre('save', function(next) {
  // Calculate actual duration if both start and end times are set
  if (this.actualStartTime && this.actualEndTime) {
    const diffMs = this.actualEndTime - this.actualStartTime;
    this.actualDuration = Math.round(diffMs / (1000 * 60)); // Convert to minutes
  }
  
  next();
});

// Static methods
bookingSchema.statics.getBookingStats = async function(businessOwnerId, startDate, endDate) {
  const matchStage = {
    businessOwner: businessOwnerId,
    isDeleted: false
  };
  
  if (startDate && endDate) {
    matchStage.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }
  
  return await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalRevenue: {
          $sum: {
            $cond: [
              { $eq: ['$status', 'completed'] },
              '$totalAmount',
              0
            ]
          }
        }
      }
    }
  ]);
};

// Instance methods
bookingSchema.methods.canBeCancelled = function() {
  return ['pending', 'confirmed'].includes(this.status);
};

bookingSchema.methods.canBeModified = function() {
  return ['pending'].includes(this.status);
};

bookingSchema.methods.addCommunication = function(fromUserId, message, type = 'message') {
  this.communications.push({
    from: fromUserId,
    message,
    type,
    timestamp: new Date()
  });
  return this.save();
};

const Booking = mongoose.models.Booking || mongoose.model('Booking', bookingSchema);

export default Booking;
