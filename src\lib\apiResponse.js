import { NextResponse } from 'next/server';

/**
 * Standardized API response utility
 * Provides consistent response format across all API endpoints
 */

export const apiResponse = {
  /**
   * Success response
   * @param {any} data - Response data
   * @param {string} message - Success message
   * @param {number} statusCode - HTTP status code (default: 200)
   * @returns {NextResponse} Formatted success response
   */
  success: (data = null, message = 'Success', statusCode = 200) => {
    const response = {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { status: statusCode });
  },

  /**
   * Error response
   * @param {string} message - Error message
   * @param {number} statusCode - HTTP status code (default: 400)
   * @param {any} errors - Additional error details
   * @returns {NextResponse} Formatted error response
   */
  error: (message = 'An error occurred', statusCode = 400, errors = null) => {
    const response = {
      success: false,
      message,
      errors,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { status: statusCode });
  },

  /**
   * Validation error response
   * @param {Array|Object} validationErrors - Validation error details
   * @param {string} message - Error message
   * @returns {NextResponse} Formatted validation error response
   */
  validationError: (validationErrors, message = 'Validation failed') => {
    const response = {
      success: false,
      message,
      errors: validationErrors,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { status: 422 });
  },

  /**
   * Unauthorized response
   * @param {string} message - Error message
   * @returns {NextResponse} Formatted unauthorized response
   */
  unauthorized: (message = 'Unauthorized access') => {
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { status: 401 });
  },

  /**
   * Forbidden response
   * @param {string} message - Error message
   * @returns {NextResponse} Formatted forbidden response
   */
  forbidden: (message = 'Access forbidden') => {
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { status: 403 });
  },

  /**
   * Not found response
   * @param {string} message - Error message
   * @returns {NextResponse} Formatted not found response
   */
  notFound: (message = 'Resource not found') => {
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { status: 404 });
  },

  /**
   * Internal server error response
   * @param {string} message - Error message
   * @param {any} error - Error details (only in development)
   * @returns {NextResponse} Formatted server error response
   */
  serverError: (message = 'Internal server error', error = null) => {
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    // Include error details only in development
    if (process.env.NODE_ENV === 'development' && error) {
      response.error = {
        message: error.message,
        stack: error.stack
      };
    }

    return NextResponse.json(response, { status: 500 });
  },

  /**
   * Rate limit exceeded response
   * @param {string} message - Error message
   * @param {number} retryAfter - Seconds to wait before retry
   * @returns {NextResponse} Formatted rate limit response
   */
  rateLimitExceeded: (message = 'Rate limit exceeded', retryAfter = 60) => {
    const response = {
      success: false,
      message,
      retryAfter,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { 
      status: 429,
      headers: {
        'Retry-After': retryAfter.toString()
      }
    });
  },

  /**
   * Paginated response
   * @param {Array} data - Array of items
   * @param {Object} pagination - Pagination info
   * @param {string} message - Success message
   * @returns {NextResponse} Formatted paginated response
   */
  paginated: (data, pagination, message = 'Data retrieved successfully') => {
    const response = {
      success: true,
      message,
      data,
      pagination: {
        page: pagination.page || 1,
        limit: pagination.limit || 10,
        total: pagination.total || 0,
        pages: pagination.pages || 0,
        hasNext: pagination.hasNext || false,
        hasPrev: pagination.hasPrev || false
      },
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { status: 200 });
  },

  /**
   * Created response
   * @param {any} data - Created resource data
   * @param {string} message - Success message
   * @returns {NextResponse} Formatted created response
   */
  created: (data, message = 'Resource created successfully') => {
    return apiResponse.success(data, message, 201);
  },

  /**
   * Updated response
   * @param {any} data - Updated resource data
   * @param {string} message - Success message
   * @returns {NextResponse} Formatted updated response
   */
  updated: (data, message = 'Resource updated successfully') => {
    return apiResponse.success(data, message, 200);
  },

  /**
   * Deleted response
   * @param {string} message - Success message
   * @returns {NextResponse} Formatted deleted response
   */
  deleted: (message = 'Resource deleted successfully') => {
    return apiResponse.success(null, message, 200);
  },

  /**
   * No content response
   * @returns {NextResponse} Formatted no content response
   */
  noContent: () => {
    return new NextResponse(null, { status: 204 });
  }
};

/**
 * Error handler middleware for API routes
 * @param {Function} handler - API route handler
 * @returns {Function} Wrapped handler with error handling
 */
export const withErrorHandler = (handler) => {
  return async (request, context) => {
    try {
      return await handler(request, context);
    } catch (error) {
      console.error('API Error:', error);

      // Handle specific error types
      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map(err => ({
          field: err.path,
          message: err.message
        }));
        return apiResponse.validationError(validationErrors);
      }

      if (error.name === 'CastError') {
        return apiResponse.error('Invalid ID format', 400);
      }

      if (error.code === 11000) {
        const field = Object.keys(error.keyPattern)[0];
        return apiResponse.error(`${field} already exists`, 409);
      }

      if (error.name === 'JsonWebTokenError') {
        return apiResponse.unauthorized('Invalid token');
      }

      if (error.name === 'TokenExpiredError') {
        return apiResponse.unauthorized('Token expired');
      }

      // Default server error
      return apiResponse.serverError('An unexpected error occurred', error);
    }
  };
};

/**
 * Validation helper
 * @param {Object} data - Data to validate
 * @param {Object} rules - Validation rules
 * @returns {Object} Validation result
 */
export const validateRequest = (data, rules) => {
  const errors = [];

  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field];

    if (rule.required && (!value || value.toString().trim() === '')) {
      errors.push({
        field,
        message: `${field} is required`
      });
      continue;
    }

    if (value && rule.type) {
      if (rule.type === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        errors.push({
          field,
          message: `${field} must be a valid email`
        });
      }

      if (rule.type === 'number' && isNaN(Number(value))) {
        errors.push({
          field,
          message: `${field} must be a number`
        });
      }

      if (rule.type === 'string' && typeof value !== 'string') {
        errors.push({
          field,
          message: `${field} must be a string`
        });
      }
    }

    if (value && rule.minLength && value.toString().length < rule.minLength) {
      errors.push({
        field,
        message: `${field} must be at least ${rule.minLength} characters`
      });
    }

    if (value && rule.maxLength && value.toString().length > rule.maxLength) {
      errors.push({
        field,
        message: `${field} cannot exceed ${rule.maxLength} characters`
      });
    }

    if (value && rule.min && Number(value) < rule.min) {
      errors.push({
        field,
        message: `${field} must be at least ${rule.min}`
      });
    }

    if (value && rule.max && Number(value) > rule.max) {
      errors.push({
        field,
        message: `${field} cannot exceed ${rule.max}`
      });
    }

    if (value && rule.enum && !rule.enum.includes(value)) {
      errors.push({
        field,
        message: `${field} must be one of: ${rule.enum.join(', ')}`
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
