import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import { sendEmail } from '@/lib/email';

export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication and super admin role (only super admins can change roles)
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'super_admin') {
      return apiResponse.error('Super admin access required', 403);
    }
    
    const userId = params.id;
    const { role } = await request.json();
    
    // Validate role
    const validRoles = ['user', 'business_owner', 'admin', 'super_admin'];
    if (!validRoles.includes(role)) {
      return apiResponse.error('Invalid role', 400);
    }
    
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return apiResponse.error('User not found', 404);
    }
    
    // Prevent changing own role
    if (userId === decoded.userId) {
      return apiResponse.error('Cannot modify your own role', 400);
    }
    
    // Store old role for email notification
    const oldRole = user.role;
    
    // Update user role
    user.role = role;
    user.updatedAt = new Date();
    
    await user.save();
    
    // Send email notification to user
    try {
      const roleNames = {
        user: 'User',
        business_owner: 'Business Owner',
        admin: 'Administrator',
        super_admin: 'Super Administrator'
      };
      
      await sendEmail({
        to: user.email,
        subject: 'Role Updated - BookMyService',
        html: `
          <h2>Role Update Notification</h2>
          <p>Dear ${user.firstName},</p>
          <p>Your account role has been updated from <strong>${roleNames[oldRole]}</strong> to <strong>${roleNames[role]}</strong>.</p>
          
          ${role === 'business_owner' ? `
            <p>As a Business Owner, you can now:</p>
            <ul>
              <li>Create and manage your services</li>
              <li>Receive and manage booking requests</li>
              <li>Track your revenue and performance</li>
              <li>Communicate with customers</li>
            </ul>
          ` : ''}
          
          ${role === 'admin' ? `
            <p>As an Administrator, you now have access to:</p>
            <ul>
              <li>User management</li>
              <li>Service oversight</li>
              <li>Platform analytics</li>
              <li>Support tools</li>
            </ul>
          ` : ''}
          
          ${role === 'user' ? `
            <p>Your account has been set to standard user access. You can browse and book services on the platform.</p>
          ` : ''}
          
          <p>Please log out and log back in for the changes to take effect.</p>
          
          <p>If you have any questions about your new role, please contact support.</p>
          
          <p>Best regards,<br>The BookMyService Team</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send role update email:', emailError);
      // Don't fail the request if email fails
    }
    
    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    
    return apiResponse.success(
      userResponse, 
      `User role updated to ${role} successfully`
    );
    
  } catch (error) {
    console.error('Error updating user role:', error);
    return apiResponse.error('Failed to update user role', 500);
  }
}
