import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Service from '@/models/Service';
import EmployeeServiceAssignment from '@/models/EmployeeServiceAssignment';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';

// Helper function to verify business owner authentication
async function verifyBusinessOwner(request) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '');
  if (!token) {
    throw new Error('Authentication required');
  }
  
  const decoded = verifyToken(token);
  if (!decoded) {
    throw new Error('Invalid token');
  }
  
  let businessOwnerId, userRole;
  
  if (decoded.user) {
    businessOwnerId = decoded.user.id;
    userRole = decoded.user.role;
  } else if (decoded.businessOwner) {
    businessOwnerId = decoded.businessOwner.id;
    userRole = decoded.businessOwner.role;
  } else {
    throw new Error('Invalid token structure');
  }
  
  if (userRole !== 'business_owner') {
    throw new Error('Unauthorized access');
  }
  
  return businessOwnerId;
}

// GET - Get employee's service assignments
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const businessOwnerId = await verifyBusinessOwner(request);
    const employeeId = params.id;
    
    // Verify employee belongs to business owner
    const employee = await Employee.findOne({
      _id: employeeId,
      businessOwner: businessOwnerId
    });
    
    if (!employee) {
      return apiResponse.error('Employee not found', 404);
    }
    
    // Get service assignments
    const assignments = await EmployeeServiceAssignment.find({
      employee: employeeId,
      businessOwner: businessOwnerId
    })
    .populate('service', 'title name category price duration status')
    .sort({ assignedDate: -1 });
    
    return apiResponse.success(assignments, 'Service assignments fetched successfully');
    
  } catch (error) {
    console.error('Error fetching service assignments:', error);
    
    if (error.message === 'Authentication required') {
      return apiResponse.error('Authentication required', 401);
    }
    if (error.message === 'Invalid token' || error.message === 'Invalid token structure') {
      return apiResponse.error('Invalid token', 401);
    }
    if (error.message === 'Unauthorized access') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    return apiResponse.error('Failed to fetch service assignments', 500);
  }
}

// POST - Assign employee to service
export async function POST(request, { params }) {
  try {
    await connectDB();
    
    const businessOwnerId = await verifyBusinessOwner(request);
    const employeeId = params.id;
    const assignmentData = await request.json();
    
    // Validate required fields
    if (!assignmentData.serviceId) {
      return apiResponse.error('Service ID is required', 400);
    }
    
    // Verify employee belongs to business owner
    const employee = await Employee.findOne({
      _id: employeeId,
      businessOwner: businessOwnerId
    });
    
    if (!employee) {
      return apiResponse.error('Employee not found', 404);
    }
    
    // Verify service belongs to business owner
    const service = await Service.findOne({
      _id: assignmentData.serviceId,
      businessOwner: businessOwnerId
    });
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    // Check if assignment already exists
    const existingAssignment = await EmployeeServiceAssignment.findOne({
      employee: employeeId,
      service: assignmentData.serviceId
    });
    
    if (existingAssignment) {
      if (existingAssignment.isActive) {
        return apiResponse.error('Employee is already assigned to this service', 400);
      } else {
        // Reactivate existing assignment
        existingAssignment.isActive = true;
        existingAssignment.role = assignmentData.role || 'primary';
        existingAssignment.specialization = assignmentData.specialization || '';
        existingAssignment.assignedDate = new Date();
        existingAssignment.assignedBy = businessOwnerId;
        
        await existingAssignment.save();
        
        const populatedAssignment = await EmployeeServiceAssignment.findById(existingAssignment._id)
          .populate('service', 'title name category price duration status')
          .populate('employee', 'firstName lastName email employeeId');
        
        return apiResponse.success(populatedAssignment, 'Employee reassigned to service successfully');
      }
    }
    
    // Create new assignment
    const assignment = new EmployeeServiceAssignment({
      employee: employeeId,
      service: assignmentData.serviceId,
      businessOwner: businessOwnerId,
      role: assignmentData.role || 'primary',
      specialization: assignmentData.specialization || '',
      permissions: assignmentData.permissions || {},
      assignedBy: businessOwnerId
    });
    
    await assignment.save();
    
    // Also update employee's assignedServices array for backward compatibility
    await employee.addServiceAssignment(assignmentData.serviceId, assignmentData.specialization);
    
    const populatedAssignment = await EmployeeServiceAssignment.findById(assignment._id)
      .populate('service', 'title name category price duration status')
      .populate('employee', 'firstName lastName email employeeId');
    
    return apiResponse.success(populatedAssignment, 'Employee assigned to service successfully', 201);
    
  } catch (error) {
    console.error('Error assigning employee to service:', error);
    
    if (error.message === 'Authentication required') {
      return apiResponse.error('Authentication required', 401);
    }
    if (error.message === 'Invalid token' || error.message === 'Invalid token structure') {
      return apiResponse.error('Invalid token', 401);
    }
    if (error.message === 'Unauthorized access') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    return apiResponse.error('Failed to assign employee to service', 500);
  }
}

// DELETE - Remove employee from service
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    const businessOwnerId = await verifyBusinessOwner(request);
    const employeeId = params.id;
    
    // Get serviceId from query parameters
    const { searchParams } = new URL(request.url);
    const serviceId = searchParams.get('serviceId');
    
    if (!serviceId) {
      return apiResponse.error('Service ID is required', 400);
    }
    
    // Verify employee belongs to business owner
    const employee = await Employee.findOne({
      _id: employeeId,
      businessOwner: businessOwnerId
    });
    
    if (!employee) {
      return apiResponse.error('Employee not found', 404);
    }
    
    // Find and deactivate assignment
    const assignment = await EmployeeServiceAssignment.findOne({
      employee: employeeId,
      service: serviceId,
      businessOwner: businessOwnerId
    });
    
    if (!assignment) {
      return apiResponse.error('Service assignment not found', 404);
    }
    
    // Deactivate assignment
    await assignment.deactivate('Removed by business owner', businessOwnerId);
    
    // Also remove from employee's assignedServices array
    await employee.removeServiceAssignment(serviceId);
    
    return apiResponse.success(
      { removedAssignment: assignment },
      'Employee removed from service successfully'
    );
    
  } catch (error) {
    console.error('Error removing employee from service:', error);
    
    if (error.message === 'Authentication required') {
      return apiResponse.error('Authentication required', 401);
    }
    if (error.message === 'Invalid token' || error.message === 'Invalid token structure') {
      return apiResponse.error('Invalid token', 401);
    }
    if (error.message === 'Unauthorized access') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    return apiResponse.error('Failed to remove employee from service', 500);
  }
}
