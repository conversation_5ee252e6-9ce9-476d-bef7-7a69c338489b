"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import Link from 'next/link';
import { toast } from 'react-toastify';

function BusinessDashboardContent() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalServices: 0,
    activeBookings: 0,
    monthlyRevenue: 0,
    customerRating: 0
  });
  const [recentBookings, setRecentBookings] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsResponse, bookingsResponse] = await Promise.all([
        fetch('/api/business/dashboard/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }),
        fetch('/api/business/dashboard/recent-bookings', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
      ]);

      const statsData = await statsResponse.json();
      const bookingsData = await bookingsResponse.json();

      if (statsData.success) {
        setStats(statsData.data);
      }

      if (bookingsData.success) {
        setRecentBookings(bookingsData.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Keep default values on error
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-64"></div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5">
          {[1, 2, 3, 4].map((item) => (
            <div key={item} className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
              <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-heading-3 font-bold text-dark dark:text-white">
          Welcome back, {user?.businessName || user?.firstName}!
        </h1>
        <p className="text-body-sm text-dark-5 dark:text-dark-6">
          Here's what's happening with your business today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5">
        <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
          <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-primary/[0.08] mb-4">
            <svg className="fill-primary dark:fill-white" width="22" height="16" viewBox="0 0 22 16" fill="none">
              <path d="M11 15.1156C4.19376 15.1156 0.825012 8.61876 0.687512 8.34376C0.584387 8.13751 0.584387 7.86251 0.687512 7.65626C0.825012 7.38126 4.19376 0.918762 11 0.918762C17.8063 0.918762 21.175 7.38126 21.3125 7.65626C21.4156 7.86251 21.4156 8.13751 21.3125 8.34376C21.175 8.61876 17.8063 15.1156 11 15.1156ZM2.26876 8.00001C3.02501 9.27189 5.98126 13.5688 11 13.5688C16.0188 13.5688 18.975 9.27189 19.7313 8.00001C18.975 6.72814 16.0188 2.43126 11 2.43126C5.98126 2.43126 3.02501 6.72814 2.26876 8.00001Z"/>
            </svg>
          </div>
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              {stats.totalServices}
            </h4>
            <span className="text-body-sm font-medium">Total Services</span>
          </div>
        </div>

        <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
          <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-blue/[0.08] mb-4">
            <svg className="fill-blue dark:fill-white" width="20" height="22" viewBox="0 0 20 22" fill="none">
              <path d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"/>
            </svg>
          </div>
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              {stats.activeBookings}
            </h4>
            <span className="text-body-sm font-medium">Active Bookings</span>
          </div>
        </div>

        <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
          <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-green/[0.08] mb-4">
            <svg className="fill-green dark:fill-white" width="20" height="22" viewBox="0 0 20 22" fill="none">
              <path d="M12 7.5a2.25 2.25 0 100 4.5 2.25 2.25 0 000-4.5z"/>
              <path fillRule="evenodd" clipRule="evenodd" d="M1.5 4.875C1.5 3.839 2.34 3 3.375 3h17.25c1.035 0 1.875.84 1.875 1.875v9.75c0 1.036-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 14.625v-9.75z"/>
            </svg>
          </div>
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              ${stats.monthlyRevenue.toLocaleString()}
            </h4>
            <span className="text-body-sm font-medium">Monthly Revenue</span>
          </div>
        </div>

        <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
          <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-orange/[0.08] mb-4">
            <svg className="fill-orange dark:fill-white" width="20" height="22" viewBox="0 0 20 22" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"/>
            </svg>
          </div>
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              {stats.customerRating}
            </h4>
            <span className="text-body-sm font-medium">Customer Rating</span>
          </div>
        </div>
      </div>

      {/* Recent Bookings */}
      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <h4 className="mb-5.5 text-body-2xlg font-bold text-dark dark:text-white">
          Recent Bookings
        </h4>
        <div className="flex flex-col">
          <div className="grid grid-cols-3 rounded-sm bg-gray-2 dark:bg-gray-dark sm:grid-cols-5">
            <div className="p-2.5 xl:p-5">
              <h5 className="text-sm font-medium uppercase xsm:text-base">Service</h5>
            </div>
            <div className="p-2.5 text-center xl:p-5">
              <h5 className="text-sm font-medium uppercase xsm:text-base">Customer</h5>
            </div>
            <div className="p-2.5 text-center xl:p-5">
              <h5 className="text-sm font-medium uppercase xsm:text-base">Date</h5>
            </div>
            <div className="hidden p-2.5 text-center sm:block xl:p-5">
              <h5 className="text-sm font-medium uppercase xsm:text-base">Status</h5>
            </div>
            <div className="hidden p-2.5 text-center sm:block xl:p-5">
              <h5 className="text-sm font-medium uppercase xsm:text-base">Amount</h5>
            </div>
          </div>

          {recentBookings.map((booking) => (
            <div key={booking.id} className="grid grid-cols-3 border-b border-stroke dark:border-dark-3 sm:grid-cols-5">
              <div className="flex items-center gap-3 p-2.5 xl:p-5">
                <p className="text-dark dark:text-white">{booking.service}</p>
              </div>

              <div className="flex items-center justify-center p-2.5 xl:p-5">
                <p className="text-dark dark:text-white">{booking.customer}</p>
              </div>

              <div className="flex items-center justify-center p-2.5 xl:p-5">
                <p className="text-dark dark:text-white">{booking.date}</p>
              </div>

              <div className="hidden items-center justify-center p-2.5 sm:flex xl:p-5">
                <span className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(booking.status)}`}>
                  {booking.status}
                </span>
              </div>

              <div className="hidden items-center justify-center p-2.5 sm:flex xl:p-5">
                <p className="text-dark dark:text-white">${booking.amount}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function BusinessDashboardPage() {
  return (
    <ProtectedRoute allowedRoles={['business_owner']}>
      <BusinessDashboardContent />
    </ProtectedRoute>
  );
}
