import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { generateOtp, hashPassword, sendOtpMail } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const {
      firstName,
      lastName,
      email,
      password,
      phoneNumber,
      role,
      // Business owner specific fields
      ownerFirstName,
      ownerLastName,
      businessName,
      businessCategory,
      businessDescription,
      businessAddress,
      city,
      state,
      zipCode,
      country
    } = await request.json();

    console.log('Registration request:', {
      email,
      role,
      businessName: businessName || 'N/A',
      firstName: firstName || ownerFirstName,
      lastName: lastName || ownerLastName
    });

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, { status: 400 });
    }

    // Determine user type and validate required fields
    const isBusinessOwner = role === 'business_owner';
    const userFirstName = isBusinessOwner ? ownerFirstName : firstName;
    const userLastName = isBusinessOwner ? ownerLastName : lastName;

    // Validate required fields
    if (!userFirstName || !email || !password) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields'
      }, { status: 400 });
    }

    if (isBusinessOwner && !businessName) {
      return NextResponse.json({
        success: false,
        message: 'Business name is required for business owners'
      }, { status: 400 });
    }

    // Generate OTP
    const otp = generateOtp();

    // Send OTP email
    await sendOtpMail(email, userFirstName, '', otp);

    // Store user data and OTP in cookies (temporary storage)
    const response = NextResponse.json({
      success: true,
      message: 'OTP sent successfully'
    });

    // Set cookies with user data and OTP
    const userData = isBusinessOwner ? {
      personalInfo: {
        ownerFirstName,
        ownerLastName,
        email,
        password,
        phoneNumber,
      },
      businessInfo: {
        businessName,
        businessCategory,
        businessDescription,
        businessAddress,
        city,
        state,
        zipCode,
        country,
      },
      role: 'business_owner'
    } : {
      firstName,
      lastName,
      email,
      password,
      phoneNumber,
      role: 'user'
    };

    const cookieName = isBusinessOwner ? 'business_owner_data' : 'user_data';

    response.cookies.set(cookieName, JSON.stringify(userData), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    response.cookies.set('otp', otp, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    return response;

  } catch (error) {
    console.error('Register Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to send OTP'
    }, { status: 500 });
  }
}
