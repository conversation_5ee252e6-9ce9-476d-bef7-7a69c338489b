import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Employee from '@/models/Employee';
import EmployeeServiceAssignment from '@/models/EmployeeServiceAssignment';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import bcrypt from 'bcryptjs';

// Helper function to verify business owner authentication
async function verifyBusinessOwner(request) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '');
  if (!token) {
    throw new Error('Authentication required');
  }
  
  const decoded = verifyToken(token);
  if (!decoded) {
    throw new Error('Invalid token');
  }
  
  let businessOwnerId, userRole;
  
  if (decoded.user) {
    businessOwnerId = decoded.user.id;
    userRole = decoded.user.role;
  } else if (decoded.businessOwner) {
    businessOwnerId = decoded.businessOwner.id;
    userRole = decoded.businessOwner.role;
  } else {
    throw new Error('Invalid token structure');
  }
  
  if (userRole !== 'business_owner') {
    throw new Error('Unauthorized access');
  }
  
  return businessOwnerId;
}

// GET - Get individual employee details
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const businessOwnerId = await verifyBusinessOwner(request);
    const employeeId = params.id;
    
    // Find employee and verify ownership
    const employee = await Employee.findOne({
      _id: employeeId,
      businessOwner: businessOwnerId
    })
    .select('-password')
    .populate('assignedServices.service', 'title name category price duration status');
    
    if (!employee) {
      return apiResponse.error('Employee not found', 404);
    }
    
    // Get service assignments with detailed info
    const serviceAssignments = await EmployeeServiceAssignment.find({
      employee: employeeId,
      isActive: true
    }).populate('service', 'title name category price duration status');
    
    // Get booking statistics
    const bookingStats = await Booking.aggregate([
      { $match: { assignedEmployee: employee._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' }
        }
      }
    ]);
    
    const totalBookings = await Booking.countDocuments({ assignedEmployee: employeeId });
    
    return apiResponse.success({
      employee,
      serviceAssignments,
      stats: {
        totalBookings,
        bookingsByStatus: bookingStats
      }
    }, 'Employee details fetched successfully');
    
  } catch (error) {
    console.error('Error fetching employee:', error);
    
    if (error.message === 'Authentication required') {
      return apiResponse.error('Authentication required', 401);
    }
    if (error.message === 'Invalid token' || error.message === 'Invalid token structure') {
      return apiResponse.error('Invalid token', 401);
    }
    if (error.message === 'Unauthorized access') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    return apiResponse.error('Failed to fetch employee', 500);
  }
}

// PUT - Update employee
export async function PUT(request, { params }) {
  try {
    await connectDB();
    
    const businessOwnerId = await verifyBusinessOwner(request);
    const employeeId = params.id;
    const updateData = await request.json();
    
    // Find employee and verify ownership
    const employee = await Employee.findOne({
      _id: employeeId,
      businessOwner: businessOwnerId
    });
    
    if (!employee) {
      return apiResponse.error('Employee not found', 404);
    }
    
    // Handle password update separately
    if (updateData.password) {
      const saltRounds = 12;
      updateData.password = await bcrypt.hash(updateData.password, saltRounds);
    }
    
    // Update employee
    const updatedEmployee = await Employee.findByIdAndUpdate(
      employeeId,
      { 
        ...updateData,
        updatedBy: businessOwnerId,
        updatedAt: new Date()
      },
      { 
        new: true, 
        runValidators: true 
      }
    ).select('-password').populate('assignedServices.service', 'title name category');
    
    return apiResponse.success(updatedEmployee, 'Employee updated successfully');
    
  } catch (error) {
    console.error('Error updating employee:', error);
    
    if (error.message === 'Authentication required') {
      return apiResponse.error('Authentication required', 401);
    }
    if (error.message === 'Invalid token' || error.message === 'Invalid token structure') {
      return apiResponse.error('Invalid token', 401);
    }
    if (error.message === 'Unauthorized access') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    if (error.name === 'CastError') {
      return apiResponse.error('Invalid employee ID', 400);
    }
    
    return apiResponse.error('Failed to update employee', 500);
  }
}

// DELETE - Delete employee (soft delete by changing status)
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    const businessOwnerId = await verifyBusinessOwner(request);
    const employeeId = params.id;
    
    // Find employee and verify ownership
    const employee = await Employee.findOne({
      _id: employeeId,
      businessOwner: businessOwnerId
    });
    
    if (!employee) {
      return apiResponse.error('Employee not found', 404);
    }
    
    // Check if employee has active bookings
    const activeBookings = await Booking.countDocuments({
      assignedEmployee: employeeId,
      status: { $in: ['pending', 'confirmed', 'in_progress'] }
    });
    
    if (activeBookings > 0) {
      return apiResponse.error(
        'Cannot delete employee with active bookings. Please complete or reassign all bookings first.',
        400
      );
    }
    
    // Soft delete - change status to terminated
    const deletedEmployee = await Employee.findByIdAndUpdate(
      employeeId,
      { 
        status: 'terminated',
        updatedBy: businessOwnerId,
        updatedAt: new Date()
      },
      { new: true }
    ).select('-password');
    
    // Deactivate all service assignments
    await EmployeeServiceAssignment.updateMany(
      { employee: employeeId },
      { 
        isActive: false,
        deactivatedDate: new Date(),
        deactivatedBy: businessOwnerId,
        deactivationReason: 'Employee terminated'
      }
    );
    
    return apiResponse.success(
      { deletedEmployee },
      'Employee deleted successfully'
    );
    
  } catch (error) {
    console.error('Error deleting employee:', error);
    
    if (error.message === 'Authentication required') {
      return apiResponse.error('Authentication required', 401);
    }
    if (error.message === 'Invalid token' || error.message === 'Invalid token structure') {
      return apiResponse.error('Invalid token', 401);
    }
    if (error.message === 'Unauthorized access') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    if (error.name === 'CastError') {
      return apiResponse.error('Invalid employee ID', 400);
    }
    
    return apiResponse.error('Failed to delete employee', 500);
  }
}

// PATCH - Update employee status or specific fields
export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    const businessOwnerId = await verifyBusinessOwner(request);
    const employeeId = params.id;
    const { action, ...updateData } = await request.json();
    
    // Find employee and verify ownership
    const employee = await Employee.findOne({
      _id: employeeId,
      businessOwner: businessOwnerId
    });
    
    if (!employee) {
      return apiResponse.error('Employee not found', 404);
    }
    
    let update = {};
    let message = '';
    
    switch (action) {
      case 'toggle-status':
        const newStatus = employee.status === 'active' ? 'inactive' : 'active';
        update.status = newStatus;
        message = `Employee status changed to ${newStatus}`;
        break;
        
      case 'suspend':
        update.status = 'suspended';
        message = 'Employee suspended';
        break;
        
      case 'reactivate':
        update.status = 'active';
        message = 'Employee reactivated';
        break;
        
      case 'update-availability':
        update.availability = updateData.availability;
        message = 'Employee availability updated';
        break;
        
      default:
        return apiResponse.error('Invalid action', 400);
    }
    
    // Update employee
    const updatedEmployee = await Employee.findByIdAndUpdate(
      employeeId,
      { 
        ...update,
        updatedBy: businessOwnerId
      },
      { new: true }
    ).select('-password');
    
    return apiResponse.success(updatedEmployee, message);
    
  } catch (error) {
    console.error('Error updating employee status:', error);
    
    if (error.message === 'Authentication required') {
      return apiResponse.error('Authentication required', 401);
    }
    if (error.message === 'Invalid token' || error.message === 'Invalid token structure') {
      return apiResponse.error('Invalid token', 401);
    }
    if (error.message === 'Unauthorized access') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    if (error.name === 'CastError') {
      return apiResponse.error('Invalid employee ID', 400);
    }
    
    return apiResponse.error('Failed to update employee', 500);
  }
}
