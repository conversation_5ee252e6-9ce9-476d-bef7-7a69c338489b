import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import { sendEmail } from '@/lib/email';

export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded) {
      return apiResponse.error('Invalid token', 401);
    }
    
    const bookingId = params.id;
    const userId = decoded.userId;
    
    // Find the booking
    const booking = await Booking.findById(bookingId)
      .populate('user', 'firstName lastName email')
      .populate('service', 'title description price')
      .populate('businessOwner', 'businessName email ownerFirstName ownerLastName');
    
    if (!booking) {
      return apiResponse.error('Booking not found', 404);
    }
    
    // Check if user owns this booking or is admin
    if (booking.user._id.toString() !== userId && 
        decoded.role !== 'admin' && 
        decoded.role !== 'super_admin') {
      return apiResponse.error('Unauthorized to cancel this booking', 403);
    }
    
    // Check if booking can be cancelled
    if (booking.status === 'cancelled') {
      return apiResponse.error('Booking is already cancelled', 400);
    }
    
    if (booking.status === 'completed') {
      return apiResponse.error('Cannot cancel a completed booking', 400);
    }
    
    // Update booking status
    booking.status = 'cancelled';
    booking.cancelledAt = new Date();
    booking.cancelledBy = userId;
    
    await booking.save();
    
    // Send email notification to business owner
    try {
      await sendEmail({
        to: booking.businessOwner.email,
        subject: `Booking Cancelled - ${booking.service.title}`,
        html: `
          <h2>Booking Cancelled</h2>
          <p>A booking for your service has been cancelled.</p>
          
          <h3>Service Details:</h3>
          <p><strong>Service:</strong> ${booking.service.title}</p>
          <p><strong>Price:</strong> $${booking.service.price}</p>
          
          <h3>Customer Details:</h3>
          <p><strong>Name:</strong> ${booking.user.firstName} ${booking.user.lastName}</p>
          <p><strong>Email:</strong> ${booking.user.email}</p>
          
          <h3>Booking Details:</h3>
          <p><strong>Booking Date:</strong> ${new Date(booking.preferredDate).toLocaleDateString()}</p>
          <p><strong>Cancelled On:</strong> ${new Date().toLocaleDateString()}</p>
          
          <p>The booking slot is now available for other customers.</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send cancellation notification email:', emailError);
    }
    
    // Send confirmation email to user
    try {
      await sendEmail({
        to: booking.user.email,
        subject: `Booking Cancelled - ${booking.service.title}`,
        html: `
          <h2>Booking Cancelled</h2>
          <p>Dear ${booking.user.firstName},</p>
          <p>Your booking has been cancelled successfully.</p>
          
          <h3>Cancelled Booking Details:</h3>
          <p><strong>Service:</strong> ${booking.service.title}</p>
          <p><strong>Provider:</strong> ${booking.businessOwner.businessName}</p>
          <p><strong>Original Date:</strong> ${new Date(booking.preferredDate).toLocaleDateString()}</p>
          <p><strong>Cancelled On:</strong> ${new Date().toLocaleDateString()}</p>
          
          <p>If you have any questions, please don't hesitate to contact us.</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send cancellation confirmation email:', emailError);
    }
    
    return apiResponse.success(booking, 'Booking cancelled successfully');
    
  } catch (error) {
    console.error('Error cancelling booking:', error);
    return apiResponse.error('Failed to cancel booking', 500);
  }
}
