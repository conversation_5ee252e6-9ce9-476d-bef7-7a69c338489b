import mongoose from 'mongoose';

const BusinessSchema = new mongoose.Schema({
  ownerFirstName: {
    type: String,
    required: [true, "The first name is required."],
  },
  ownerLastName: {
    type: String,
    required: [true, "The last name is required."],
  },
  email: {
    type: String,
    required: [true, "Email is required."],
    unique: true,
    lowercase: true,
  },
  password: {
    type: String,
    required: [true, "Password is required."],
  },
  phoneNumber: {
    type: String,
    required: [true, "Phone number is required."],
    unique: true,
    validate: {
      validator: function (v) {
        return /\d{10}/.test(v);
      },
      message: (props) => `${props.value} is not a valid phone number!`,
    },
  },
  businessName: {
    type: String,
    required: [true, "Business name is required."],
  },
  businessCategory: {
    type: String,
    required: [true, "Business category is required."],
  },
  businessDescription: {
    type: String,
    default: "",
  },
  businessAddress: {
    type: String,
    required: true,
  },
  city: {
    type: String,
    required: true,
  },
  state: {
    type: String,
    required: true,
  },
  zipCode: {
    type: String,
    required: true,
  },
  country: {
    type: String,
    required: true,
  },
  businessWebsite: {
    type: String,
    default: null,
  },
  businessLogo: {
    type: String,
    default: "https://example.com/default-business-logo.png",
  },
  role: {
    type: String,
    default: "business_owner",
  },
  servicesOffered: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Service" 
  }],
  workingHours: {
    type: String,
    required: false,
  },
  businessRegistrationNumber: {
    type: String,
    default: null,
  },
  taxId: {
    type: String,
    default: null,
  },
  businessLicense: {
    type: String,
    default: null,
  },
  idProof: {
    type: String,
    default: null,
  },
  paymentMethod: {
    type: String,
    required: false,
    enum: ["Bank Transfer", "PayPal", "Stripe"],
  },
  payoutDetails: {
    type: String,
    required: false,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  isEmailVerified: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  termsAccepted: {
    type: Boolean,
    required: false,
  },
  // Social login fields
  googleId: {
    type: String,
    unique: true,
    sparse: true, // Allow multiple null values
  },
  facebookId: {
    type: String,
    unique: true,
    sparse: true, // Allow multiple null values
  },
  authProvider: {
    type: String,
    enum: ['local', 'google', 'facebook'],
    default: 'local'
  },
  // OTP for verification
  otp: String,
  otpExpires: Date,
}, {
  timestamps: true,
});

// Indexes
BusinessSchema.index({ email: 1 });
BusinessSchema.index({ businessCategory: 1 });
BusinessSchema.index({ city: 1 });
BusinessSchema.index({ state: 1 });
BusinessSchema.index({ googleId: 1 });
BusinessSchema.index({ facebookId: 1 });

// Instance methods
BusinessSchema.methods.generateAuthToken = function () {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    { businessOwnerId: this._id, role: this.role },
    process.env.JWT_SECRET || 'your-secret-key',
    {
      expiresIn: "24h",
    }
  );
};

BusinessSchema.methods.toSafeObject = function() {
  const businessOwnerObject = this.toObject();
  delete businessOwnerObject.password;
  delete businessOwnerObject.otp;
  delete businessOwnerObject.otpExpires;
  return businessOwnerObject;
};

// Virtual for full name
BusinessSchema.virtual('ownerFullName').get(function() {
  return `${this.ownerFirstName} ${this.ownerLastName}`;
});

// Virtual for full address
BusinessSchema.virtual('fullAddress').get(function() {
  return `${this.businessAddress}, ${this.city}, ${this.state} ${this.zipCode}, ${this.country}`;
});

// Ensure virtual fields are serialized
BusinessSchema.set('toJSON', { virtuals: true });
BusinessSchema.set('toObject', { virtuals: true });

export default mongoose.models.BusinessOwner || mongoose.model("BusinessOwner", BusinessSchema);
