﻿import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import BusinessOwner from '@/models/BusinessOwner';
import { generateOtp, hashPassword, sendOtpMail } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();

    const {
      ownerFirstName,
      ownerLastName,
      email,
      password,
      phoneNumber,
      businessName,
      businessCategory,
      businessDescription,
      businessAddress,
      city,
      state,
      zipCode,
      country
    } = await request.json();

    console.log('Business Owner Registration request:', {
      email,
      businessName,
      ownerFirstName,
      ownerLastName
    });

    // Check if business owner already exists
    const existingBusinessOwner = await BusinessOwner.findOne({ email });
    if (existingBusinessOwner) {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, { status: 400 });
    }

    // Validate required fields
    if (!ownerFirstName || !ownerLastName || !email || !password || !businessName) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields'
      }, { status: 400 });
    }

    // Generate OTP
    const otp = generateOtp();

    // Send OTP email
    await sendOtpMail(email, ownerFirstName, '', otp);

    // Store business owner data and OTP in cookies (temporary storage)
    const response = NextResponse.json({
      success: true,
      message: 'OTP sent successfully'
    });

    const businessOwnerData = {
      personalInfo: {
        ownerFirstName,
        ownerLastName,
        email,
        password,
        phoneNumber,
      },
      businessInfo: {
        businessName,
        businessCategory,
        businessDescription,
        businessAddress,
        city,
        state,
        zipCode,
        country,
      },
      role: 'business_owner'
    };

    response.cookies.set('business_owner_data', JSON.stringify(businessOwnerData), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    response.cookies.set('otp', otp, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    return response;

  } catch (error) {
    console.error('Business Owner Register Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to send OTP'
    }, { status: 500 });
  }
}
