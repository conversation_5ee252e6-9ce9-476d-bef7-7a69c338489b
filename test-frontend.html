<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Role Selection</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input { padding: 8px; border: 1px solid #ccc; border-radius: 4px; width: 200px; }
        .business-fields { background-color: #f0f0f0; padding: 15px; margin-top: 15px; border-radius: 4px; }
        .hidden { display: none; }
        .debug { background-color: #e8f4f8; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Test Role Selection Frontend</h1>
    
    <div class="debug">
        <h3>Debug Info:</h3>
        <div id="debug-output">Role: Not selected</div>
    </div>

    <form id="test-form">
        <div class="form-group">
            <label for="role">Role:</label>
            <select id="role" name="role" required>
                <option value="" disabled selected>Select a role</option>
                <option value="user">User</option>
                <option value="business_owner">Business Owner</option>
            </select>
        </div>

        <div class="form-group">
            <label for="firstName">First Name:</label>
            <input type="text" id="firstName" name="firstName" required>
        </div>

        <div class="form-group">
            <label for="lastName">Last Name:</label>
            <input type="text" id="lastName" name="lastName" required>
        </div>

        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>

        <div id="business-fields" class="business-fields hidden">
            <h3>Business Owner Fields</h3>
            
            <div class="form-group">
                <label for="businessName">Business Name:</label>
                <input type="text" id="businessName" name="businessName">
            </div>

            <div class="form-group">
                <label for="businessCategory">Business Category:</label>
                <select id="businessCategory" name="businessCategory">
                    <option value="">Select Category</option>
                    <option value="Cleaning">Cleaning</option>
                    <option value="Plumbing">Plumbing</option>
                    <option value="Electrical">Electrical</option>
                    <option value="Gardening">Gardening</option>
                </select>
            </div>

            <div class="form-group">
                <label for="businessDescription">Business Description:</label>
                <input type="text" id="businessDescription" name="businessDescription">
            </div>
        </div>

        <button type="button" onclick="testRegistration()">Test Registration</button>
    </form>

    <div class="debug">
        <h3>Form Data:</h3>
        <pre id="form-data-output">No data</pre>
    </div>

    <script>
        const roleSelect = document.getElementById('role');
        const businessFields = document.getElementById('business-fields');
        const debugOutput = document.getElementById('debug-output');
        const formDataOutput = document.getElementById('form-data-output');

        // Handle role change
        roleSelect.addEventListener('change', function(e) {
            const selectedRole = e.target.value;
            console.log('Role changed to:', selectedRole);
            
            debugOutput.textContent = `Role: ${selectedRole}`;
            
            if (selectedRole === 'business_owner') {
                businessFields.classList.remove('hidden');
                console.log('Showing business owner fields');
            } else {
                businessFields.classList.add('hidden');
                console.log('Hiding business owner fields');
            }
            
            updateFormData();
        });

        // Update form data display
        function updateFormData() {
            const formData = new FormData(document.getElementById('test-form'));
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            formDataOutput.textContent = JSON.stringify(data, null, 2);
        }

        // Test registration
        async function testRegistration() {
            const formData = new FormData(document.getElementById('test-form'));
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            console.log('Testing registration with data:', data);
            
            if (!data.role) {
                alert('Please select a role first!');
                return;
            }
            
            const endpoint = data.role === 'business_owner' 
                ? '/api/auth/registerBusinessOwner'
                : '/api/auth/register';
            
            try {
                const response = await fetch(`http://localhost:3000${endpoint}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                console.log('Registration result:', result);
                alert(`Registration ${result.success ? 'successful' : 'failed'}: ${result.message}`);
            } catch (error) {
                console.error('Registration error:', error);
                alert('Registration failed: ' + error.message);
            }
        }

        // Add event listeners to all form fields
        document.querySelectorAll('input, select').forEach(field => {
            field.addEventListener('input', updateFormData);
            field.addEventListener('change', updateFormData);
        });
    </script>
</body>
</html>
