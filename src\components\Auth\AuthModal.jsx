"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { showSuccessToast, showErrorToast } from "@/utils/toast";
import Modal from "@/components/UI/Modal";
import InputGroup from "@/components/FormElements/InputGroup";
import { Select } from "@/components/FormElements/select";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import Button from "@/components/FormElements/Button";

const AuthModal = ({ isOpen, onClose }) => {
  const router = useRouter();
  const { login, setUser } = useAuth();
  const [isLogin, setIsLogin] = useState(true);
  const [verificationSent, setVerificationSent] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    phoneNumber: "",
    firstName: "",
    lastName: "",
    role: "user", // Default to user
    otp: "",
    businessName: "", // Added for BusinessOwner
    businessCategory: "", // Added for BusinessOwner
    businessDescription: "", // Added for BusinessOwner
    businessAddress: "", // Added for BusinessOwner
    city: "", // Added for BusinessOwner
    state: "", // Added for BusinessOwner
    zipCode: "", // Added for BusinessOwner
    country: "", // Added for BusinessOwner
  });

  const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setError("");
  };

  const resetForm = () => {
    setFormData({
      email: "",
      password: "",
      phoneNumber: "",
      firstName: "",
      lastName: "",
      role: "user",
      otp: "",
      businessName: "",
      businessCategory: "",
      businessDescription: "",
      businessAddress: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
    });
    setError("");
    setVerificationSent(false);
    setIsLogin(true);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  // const handleSubmit = async (e) => {
  //   e.preventDefault();
  //   setError("");
  //   setLoading(true);

  //   try {
  //     if (verificationSent) {
  //       // OTP Verification
  //       const endpoint =
  //         formData.role === "business_owner"
  //           ? "/api/auth/verifyAndCreateBusinessOwner"
  //           : "/api/auth/verifyAndCreateUser";
        
  //       console.log("Sending OTP for verification:", formData.otp);
  //       const response = await fetch(`${BACKEND_URL}${endpoint}`, {
  //         method: "POST",
  //         headers: { "Content-Type": "application/json" },
  //         credentials: "include",
  //         body: JSON.stringify({ otp: formData.otp }),
  //       });

  //       const data = await response.json();
  //       console.log("OTP Verification Response:", data);

  //       if (!response.ok)
  //         throw new Error(data.message || "OTP verification failed");

  //       const token = data.data?.token || data.token;
  //       if (!token) throw new Error("No token received from server");

  //       const userData =
  //         data.data?.businessOwner || data.data?.user || data.user;
  //       setUser(userData);
  //       localStorage.setItem("token", token);

  //       showSuccessToast("Account verified successfully!");

  //       // Check if the user is a business owner
  //       if (userData.role === "business_owner") {
  //         console.log("Navigating to business profile for role:", userData.role);
  //         router.push("/business/dashboard");
  //       } else if (userData.role === "admin") {
  //         console.log("Navigating to admin dashboard for role:", userData.role);
  //         router.push("/admin/dashboard");
  //       } else {
  //         console.log("Navigating to home for role:", userData.role);
  //         router.push("/");
  //       }
        
  //       handleClose();
  //     } else if (isLogin) {
  //       // Login
  //       const endpoint =
  //         formData.role === "business_owner"
  //           ? "/api/auth/businessOwnerLogin"
  //           : "/api/auth/login";
  //       const payload = { email: formData.email, password: formData.password };
  //       console.log("Login Endpoint:", endpoint);
  //       console.log("Login Payload:", payload);
  //       console.log("Selected Role:", formData.role);

  //       const response = await fetch(`${BACKEND_URL}${endpoint}`, {
  //         method: "POST",
  //         headers: { "Content-Type": "application/json" },
  //         credentials: "include",
  //         body: JSON.stringify(payload),
  //       });

  //       const data = await response.json();
  //       console.log("Login Response:", data);

  //       if (!response.ok) throw new Error(data.message || "Login failed");

  //       const token = data.data?.token || data.token;
  //       if (!token) throw new Error("No token received from server");

  //       const userData =
  //         data.data?.businessOwner || data.data?.user || data.user;
  //       setUser(userData);
  //       localStorage.setItem("token", token);

  //       showSuccessToast(`Welcome back, ${userData.firstName || userData.ownerFirstName || 'User'}!`);

  //       // Check if the user is a business owner
  //       if (userData.role === "business_owner") {
  //         console.log("Navigating to business profile for role:", userData.role);
  //         router.push("/business/dashboard");
  //       } else if (userData.role === "admin") {
  //         console.log("Navigating to admin dashboard for role:", userData.role);
  //         router.push("/admin/dashboard");
  //       } else {
  //         console.log("Navigating to home for role:", userData.role);
  //         router.push("/");
  //       }
        
  //       handleClose();
  //     } else {
  //       // Registration
  //       const endpoint =
  //         formData.role === "business_owner"
  //           ? "/api/auth/registerBusinessOwner"
  //           : "/api/auth/register";
  //       const payload =
  //         formData.role === "business_owner"
  //           ? {
  //               email: formData.email,
  //               password: formData.password,
  //               phoneNumber: formData.phoneNumber,
  //               ownerFirstName: formData.firstName,
  //               ownerLastName: formData.lastName,
  //               businessName: formData.businessName,
  //               businessCategory: formData.businessCategory,
  //               businessDescription: formData.businessDescription,
  //               businessAddress: formData.businessAddress,
  //               city: formData.city,
  //               state: formData.state,
  //               zipCode: formData.zipCode,
  //               country: formData.country,
  //             }
  //           : {
  //               email: formData.email,
  //               password: formData.password,
  //               phoneNumber: formData.phoneNumber,
  //               firstName: formData.firstName,
  //               lastName: formData.lastName,
  //             };

  //       const response = await fetch(`${BACKEND_URL}${endpoint}`, {
  //         method: "POST",
  //         headers: { "Content-Type": "application/json" },
  //         credentials: "include",
  //         body: JSON.stringify(payload),
  //       });

  //       const data = await response.json();
  //       console.log("Registration Response:", data);

  //       if (!response.ok)
  //         throw new Error(data.message || "Registration failed");

  //       showSuccessToast("Registration successful! Please verify your email with the OTP sent to your email address.");
  //       setVerificationSent(true);
  //     }
  //   } catch (err) {
  //     setError(err.message);
  //     showErrorToast(err.message || "An error occurred");
  //     console.error("Auth Error:", err);
  //   } finally {
  //     setLoading(false);
  //   }
  // };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);
  
    try {
      if (verificationSent) {
        // OTP Verification - Use unified endpoint
        const endpoint = "/api/auth/verifyAndCreateUser";

        console.log("Verification Endpoint:", endpoint, "OTP:", formData.otp, "Role:", formData.role);
        const response = await fetch(`${BACKEND_URL}${endpoint}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({ otp: formData.otp }),
        });
  
        const data = await response.json();
        console.log("OTP Verification Response:", data);
  
        if (!response.ok)
          throw new Error(data.message || "OTP verification failed");
  
        const token = data.data?.token || data.token;
        if (!token) throw new Error("No token received from server");
  
        const userData =
          data.data?.businessOwner || data.data?.user || data.user;
        setUser(userData);
        localStorage.setItem("token", token);
  
        showSuccessToast("Account verified successfully!");
  
        if (userData.role === "business_owner") {
          console.log("Navigating to business profile for role:", userData.role);
          router.push("/business/dashboard");
        } else if (userData.role === "admin") {
          console.log("Navigating to admin dashboard for role:", userData.role);
          router.push("/admin/dashboard");
        } else {
          console.log("Navigating to home for role:", userData.role);
          router.push("/");
        }
        
        handleClose();
      } else if (isLogin) {
        // Login
        const endpoint =
          formData.role === "business_owner"
            ? "/api/auth/businessOwnerLogin"
            : "/api/auth/login";
        const payload = { email: formData.email, password: formData.password };
        console.log("Login Endpoint:", endpoint, "Payload:", payload, "Role:", formData.role);
  
        const response = await fetch(`${BACKEND_URL}${endpoint}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify(payload),
        });
  
        const data = await response.json();
        console.log("Login Response:", data);
  
        if (!response.ok) throw new Error(data.message || "Login failed");
  
        const token = data.data?.token || data.token;
        if (!token) throw new Error("No token received from server");
  
        const userData =
          data.data?.businessOwner || data.data?.user || data.user;
        setUser(userData);
        localStorage.setItem("token", token);
  
        showSuccessToast(`Welcome back, ${userData.firstName || userData.ownerFirstName || 'User'}!`);
  
        if (userData.role === "business_owner") {
          console.log("Navigating to business profile for role:", userData.role);
          router.push("/business/dashboard");
        } else if (userData.role === "admin") {
          console.log("Navigating to admin dashboard for role:", userData.role);
          router.push("/admin/dashboard");
        } else {
          console.log("Navigating to home for role:", userData.role);
          router.push("/");
        }
        
        handleClose();
      } else {
        // Registration - Use unified endpoint
        const endpoint = "/api/auth/register";
        const payload =
          formData.role === "business_owner"
            ? {
                email: formData.email,
                password: formData.password,
                phoneNumber: formData.phoneNumber,
                ownerFirstName: formData.firstName,
                ownerLastName: formData.lastName,
                businessName: formData.businessName,
                businessCategory: formData.businessCategory,
                businessDescription: formData.businessDescription,
                businessAddress: formData.businessAddress,
                city: formData.city,
                state: formData.state,
                zipCode: formData.zipCode,
                country: formData.country,
                role: "business_owner",
              }
            : {
                email: formData.email,
                password: formData.password,
                phoneNumber: formData.phoneNumber,
                firstName: formData.firstName,
                lastName: formData.lastName,
                role: "user",
              };
        
        console.log("Registration Endpoint:", endpoint, "Payload:", payload, "Role:", formData.role);
        const response = await fetch(`${BACKEND_URL}${endpoint}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify(payload),
        });
  
        const data = await response.json();
        console.log("Registration Response:", data);
  
        if (!response.ok)
          throw new Error(data.message || "Registration failed");
  
        showSuccessToast("Registration successful! Please verify your email with the OTP sent to your email address.");
        setVerificationSent(true);
      }
    } catch (err) {
      setError(err.message);
      showErrorToast(err.message || "An error occurred");
      console.error("Auth Error:", err);
    } finally {
      setLoading(false);
    }
  };
  const roleOptions = [
    { value: "user", label: "User" },
    { value: "business_owner", label: "Business Owner" },
  ];

  const businessCategories = [
    { value: "Cleaning", label: "Cleaning" },
    { value: "Repair & Maintenance", label: "Repair & Maintenance" },
    { value: "Home & Garden", label: "Home & Garden" },
    { value: "Health & Wellness", label: "Health & Wellness" },
    { value: "Technology", label: "Technology" },
    { value: "Other", label: "Other" },
  ];

  const getModalTitle = () => {
    if (verificationSent) return "Verify OTP";
    return isLogin ? "Login" : "Register";
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={getModalTitle()}
      size="lg"
      className="max-h-[90vh] overflow-y-auto"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/20 dark:text-red-400">
            {error}
          </div>
        )}

        {!verificationSent && (
          <Select
            label="Role"
            items={roleOptions}
            value={formData.role}
            onChange={handleChange}
            name="role"
            required
          />
        )}

        {verificationSent ? (
          <InputGroup
            label="Enter OTP"
            type="text"
            name="otp"
            placeholder="Enter OTP"
            value={formData.otp}
            handleChange={handleChange}
            required
          />
        ) : !isLogin ? (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <InputGroup
              label="First Name"
              type="text"
              name="firstName"
              placeholder="First Name"
              value={formData.firstName}
              handleChange={handleChange}
              required
            />
            <InputGroup
              label="Last Name"
              type="text"
              name="lastName"
              placeholder="Last Name"
              value={formData.lastName}
              handleChange={handleChange}
              required
            />
            <div className="md:col-span-2">
              <InputGroup
                label="Phone Number"
                type="tel"
                name="phoneNumber"
                placeholder="Phone Number"
                value={formData.phoneNumber}
                handleChange={handleChange}
                required
              />
            </div>
            
            {formData.role === "business_owner" && (
              <>
                <div className="md:col-span-2">
                  <InputGroup
                    label="Business Name"
                    type="text"
                    name="businessName"
                    placeholder="Business Name"
                    value={formData.businessName}
                    handleChange={handleChange}
                    required
                  />
                </div>
                <Select
                  label="Business Category"
                  items={businessCategories}
                  value={formData.businessCategory}
                  onChange={handleChange}
                  name="businessCategory"
                  placeholder="Select Category"
                  required
                />
                <InputGroup
                  label="Business Address"
                  type="text"
                  name="businessAddress"
                  placeholder="Business Address"
                  value={formData.businessAddress}
                  handleChange={handleChange}
                  required
                />
                <InputGroup
                  label="City"
                  type="text"
                  name="city"
                  placeholder="City"
                  value={formData.city}
                  handleChange={handleChange}
                  required
                />
                <InputGroup
                  label="State"
                  type="text"
                  name="state"
                  placeholder="State"
                  value={formData.state}
                  handleChange={handleChange}
                  required
                />
                <InputGroup
                  label="Zip Code"
                  type="text"
                  name="zipCode"
                  placeholder="Zip Code"
                  value={formData.zipCode}
                  handleChange={handleChange}
                  required
                />
                <InputGroup
                  label="Country"
                  type="text"
                  name="country"
                  placeholder="Country"
                  value={formData.country}
                  handleChange={handleChange}
                  required
                />
                <div className="md:col-span-2">
                  <TextAreaGroup
                    label="Business Description"
                    placeholder="Describe your business and services..."
                    value={formData.businessDescription}
                    onChange={handleChange}
                    name="businessDescription"
                    rows={4}
                  />
                </div>
              </>
            )}
          </div>
        ) : null}

        {!verificationSent && (
          <>
            <InputGroup
              label="Email"
              type="email"
              name="email"
              placeholder="Email"
              value={formData.email}
              handleChange={handleChange}
              required
            />
            <InputGroup
              label="Password"
              type="password"
              name="password"
              placeholder="Password"
              value={formData.password}
              handleChange={handleChange}
              required
            />
          </>
        )}

        <Button
          type="submit"
          loading={loading}
          className="w-full"
          size="lg"
        >
          {verificationSent ? "Verify OTP" : isLogin ? "Login" : "Register"}
        </Button>

        {!verificationSent && (
          <>
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-2 text-gray-500 dark:bg-gray-dark dark:text-gray-400">
                  Or continue with
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  window.open(`${BACKEND_URL}/api/auth/google`, "_self");
                }}
                className="w-full"
              >
                <svg className="mr-2 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Google
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  window.open(`${BACKEND_URL}/api/auth/facebook`, "_self");
                }}
                className="w-full"
              >
                <svg className="mr-2 h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                Facebook
              </Button>
            </div>

            <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            <button
              type="button"
              onClick={() => {
                console.log('Switching isLogin to:', !isLogin);
                setIsLogin(!isLogin);
                setVerificationSent(false);
                setError("");
              }}
              className="text-primary hover:underline"
            >
              {isLogin ? "Need an account? Register" : "Have an account? Login"}
            </button>
            </p>
          </>
        )}
      </form>
    </Modal>
  );
};

export default AuthModal;
