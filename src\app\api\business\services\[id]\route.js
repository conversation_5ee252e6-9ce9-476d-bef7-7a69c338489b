import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';

// GET individual service
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const businessOwnerId = decoded.userId;
    const serviceId = params.id;
    
    // Find service and verify ownership
    const service = await Service.findOne({
      _id: serviceId,
      businessOwner: businessOwnerId
    }).populate('businessOwner', 'businessName ownerFirstName ownerLastName email');
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    // Get booking statistics for this service
    const bookingStats = await Booking.aggregate([
      { $match: { service: service._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$totalAmount' }
        }
      }
    ]);
    
    const totalBookings = await Booking.countDocuments({ service: serviceId });
    
    return apiResponse.success({
      service,
      stats: {
        totalBookings,
        bookingsByStatus: bookingStats
      }
    }, 'Service details fetched successfully');
    
  } catch (error) {
    console.error('Error fetching service:', error);
    return apiResponse.error('Failed to fetch service', 500);
  }
}

// PUT - Update service
export async function PUT(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const businessOwnerId = decoded.userId;
    const serviceId = params.id;
    const updateData = await request.json();
    
    // Find service and verify ownership
    const service = await Service.findOne({
      _id: serviceId,
      businessOwner: businessOwnerId
    });
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    // Validate required fields if they're being updated
    const requiredFields = ['title', 'name', 'description', 'category', 'price', 'duration'];
    for (const field of requiredFields) {
      if (updateData.hasOwnProperty(field) && !updateData[field]) {
        return apiResponse.error(`${field} is required`, 400);
      }
    }
    
    // Update service
    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      { 
        ...updateData,
        updatedAt: new Date()
      },
      { 
        new: true, 
        runValidators: true 
      }
    ).populate('businessOwner', 'businessName ownerFirstName ownerLastName email');
    
    return apiResponse.success(updatedService, 'Service updated successfully');
    
  } catch (error) {
    console.error('Error updating service:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    if (error.name === 'CastError') {
      return apiResponse.error('Invalid service ID', 400);
    }
    
    return apiResponse.error('Failed to update service', 500);
  }
}

// DELETE service
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const businessOwnerId = decoded.userId;
    const serviceId = params.id;
    
    // Find service and verify ownership
    const service = await Service.findOne({
      _id: serviceId,
      businessOwner: businessOwnerId
    });
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    // Check if service has active bookings
    const activeBookings = await Booking.countDocuments({
      service: serviceId,
      status: { $in: ['pending', 'confirmed', 'in_progress'] }
    });
    
    if (activeBookings > 0) {
      return apiResponse.error(
        'Cannot delete service with active bookings. Please complete or cancel all bookings first.',
        400
      );
    }
    
    // Delete the service
    await Service.findByIdAndDelete(serviceId);
    
    return apiResponse.success(
      { deletedServiceId: serviceId },
      'Service deleted successfully'
    );
    
  } catch (error) {
    console.error('Error deleting service:', error);
    
    if (error.name === 'CastError') {
      return apiResponse.error('Invalid service ID', 400);
    }
    
    return apiResponse.error('Failed to delete service', 500);
  }
}

// PATCH - Toggle service status
export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const businessOwnerId = decoded.userId;
    const serviceId = params.id;
    const { action } = await request.json();
    
    // Find service and verify ownership
    const service = await Service.findOne({
      _id: serviceId,
      businessOwner: businessOwnerId
    });
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    let updateData = {};
    
    if (action === 'toggle-status') {
      // Toggle between active and inactive
      updateData.status = service.status === 'active' ? 'inactive' : 'active';
    } else if (action === 'toggle-featured') {
      // Toggle featured status
      updateData.isFeatured = !service.isFeatured;
    } else {
      return apiResponse.error('Invalid action', 400);
    }
    
    // Update service
    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      updateData,
      { new: true }
    ).populate('businessOwner', 'businessName ownerFirstName ownerLastName email');
    
    const actionMessage = action === 'toggle-status' 
      ? `Service status changed to ${updateData.status}`
      : `Service ${updateData.isFeatured ? 'marked as featured' : 'removed from featured'}`;
    
    return apiResponse.success(updatedService, actionMessage);
    
  } catch (error) {
    console.error('Error updating service status:', error);
    
    if (error.name === 'CastError') {
      return apiResponse.error('Invalid service ID', 400);
    }
    
    return apiResponse.error('Failed to update service', 500);
  }
}
