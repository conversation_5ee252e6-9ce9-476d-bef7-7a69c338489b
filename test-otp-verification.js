// Test script for OTP verification
const fetch = require('node-fetch');

const testOTPVerification = async () => {
  try {
    console.log('Testing OTP Verification...');
    
    // Use the OTP from the server logs: 110757
    const otpData = {
      otp: "110757"
    };
    
    // Step 2: Verify OTP and create business owner
    const verifyResponse = await fetch('http://localhost:3000/api/auth/verifyAndCreateUser', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(otpData)
    });

    const verifyResult = await verifyResponse.json();
    console.log('OTP Verification Response:', verifyResult);

    if (!verifyResult.success) {
      console.error('OTP Verification failed:', verifyResult.message);
      return;
    }

    console.log('✅ Business Owner created successfully!');
    console.log('User ID:', verifyResult.data.user.id);
    console.log('Business Name:', verifyResult.data.user.businessName);
    console.log('Role:', verifyResult.data.user.role);
    
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test
testOTPVerification();
